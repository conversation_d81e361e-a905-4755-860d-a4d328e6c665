# build output
dist/
# generated types
.astro/

# dependencies
node_modules/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# script logs and records
scripts/r2-metadata-update.log
scripts/coloring-page-processor.log
scripts/upload-record.json
scripts/r2-processed-files.json

# environment variables
.env
.env.production

# macOS-specific files
.DS_Store

# jetbrains setting folder
.idea/

# Vercel build output
.vercel/
.output/

.scripts/backups/
/scripts/backups/
/scripts/resource/
src/assets/coloring-pages/**/*.png
src/assets/coloring-pages/**/*.pdf
src/assets/coloring-pages/**/*.webp
src/assets/favicon/
src/assets/logo/
assists/
