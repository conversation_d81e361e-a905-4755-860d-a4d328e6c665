import { defineConfig } from 'astro/config';
import tailwindcss from '@tailwindcss/vite';
import sitemap from '@astrojs/sitemap';
import robotsTxt from 'astro-robots-txt';
import mdx from '@astrojs/mdx';
import path from 'path';
import vercel from '@astrojs/vercel';

// https://astro.build/config
export default defineConfig({
  site: 'https://www.printablecoloringhub.com',
  vite: {
    plugins: [tailwindcss()],
    resolve: {
      alias: {
        '@': path.resolve('./src')
      }
    },
    assetsInclude: ['**/*.svg'],
  },
  image: {
    service: {
      entrypoint: 'astro/assets/services/sharp',
      config: {
        limitInputPixels: false,
        limitOutputPixels: false,
        simd: true
      }
    },
    domains: [
      'www.printablecoloringhub.com',
      'static.printablecoloringhub.com'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'static.printablecoloringhub.com'
      }
    ]
  },

  integrations: [
    sitemap({
      changefreq: 'weekly',
      priority: 0.7,
      lastmod: new Date()
    }),
    robotsTxt({
      policy: [
        {
          userAgent: '*',
          allow: '/',
        }
      ],
      sitemap: true,
    }),
    mdx()
  ],
  output: 'server',
  adapter: vercel({
    webAnalytics: { enabled: true },
    imageService: false
  })
});