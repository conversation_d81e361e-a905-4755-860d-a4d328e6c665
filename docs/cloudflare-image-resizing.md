# Cloudflare 图像大小调整功能使用指南

本文档介绍如何使用 Cloudflare 的图像大小调整功能来优化 R2 存储桶中的图片，提高网站加载速度和用户体验。

## 目录

1. [Cloudflare 图像大小调整功能](#cloudflare-图像大小调整功能)
2. [设置步骤](#设置步骤)
3. [URL 参数](#url-参数)
4. [使用组件](#使用组件)
5. [最佳实践](#最佳实践)
6. [故障排除](#故障排除)

## Cloudflare 图像大小调整功能

Cloudflare 的图像大小调整功能是一项强大的服务，可以在边缘节点动态处理图像，提供以下优势：

- **动态调整大小**：根据需要调整图像尺寸，无需预先处理和存储多个版本
- **格式转换**：自动将图像转换为最佳格式（WebP、AVIF 等）
- **质量优化**：根据需要调整图像质量，减少文件大小
- **裁剪和缩放**：支持多种裁剪和缩放方式
- **边缘缓存**：优化后的图像会在 Cloudflare 的边缘节点缓存，提高加载速度

## 设置步骤

1. **确保 R2 存储桶可以公开访问**
   - 登录 Cloudflare 控制面板
   - 进入 R2 > 存储桶 > 你的存储桶
   - 确保"公开访问"已启用

2. **配置自定义域名**
   - 在 R2 > 存储桶 > 你的存储桶 > 设置 > 公开访问 中配置自定义域名
   - 添加 `static.printablecoloringhub.com` 作为自定义域名

3. **启用图像大小调整功能**
   - 在 Cloudflare 控制面板中，选择你的域名
   - 导航到 Speed > Optimization
   - 在 Image Optimization 部分，启用 Image Resizing

## URL 参数

Cloudflare 图像大小调整功能通过 URL 参数来控制图像处理。基本格式如下：

```
https://static.printablecoloringhub.com/cdn-cgi/image/width=500,height=500,fit=cover,quality=80,format=auto/coloring-pages/animals/dogs/bulldog/monochrome.png
```

### 支持的参数

| 参数 | 描述 | 示例 | 默认值 |
|------|------|------|--------|
| `width` | 图像宽度（像素） | `width=500` | 原始宽度 |
| `height` | 图像高度（像素） | `height=500` | 原始高度 |
| `fit` | 适应方式 | `fit=cover` | `scale-down` |
| `quality` | 图像质量（1-100） | `quality=80` | `85` |
| `format` | 输出格式 | `format=auto` | 原始格式 |
| `dpr` | 设备像素比 | `dpr=2` | `1` |
| `gravity` | 裁剪焦点 | `gravity=auto` | `auto` |

### 适应方式（fit）

- `scale-down`：按比例缩小以适应指定尺寸，如果原图小于指定尺寸则不放大
- `contain`：保持宽高比，完整显示图片，可能会有留白
- `cover`：保持宽高比，裁剪图片以填满指定尺寸
- `crop`：裁剪到精确尺寸，可能会改变宽高比
- `pad`：保持宽高比，用背景色填充空白区域以达到指定尺寸

### 裁剪焦点（gravity）

- `auto`：自动检测图像中的重要内容
- `center`：居中裁剪
- `north`：顶部裁剪
- `south`：底部裁剪
- `east`：右侧裁剪
- `west`：左侧裁剪

## 使用组件

我们创建了几个组件来简化 Cloudflare 图像大小调整功能的使用：

### CloudflareOptimizedImage 组件

这是一个带有渐进式加载和占位符的图像组件：

```astro
<CloudflareOptimizedImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  width={500}
  height={500}
  preset="card"
  format="auto"
  showPlaceholder={true}
/>
```

### CloudflareImage 组件

这是一个简化的图像组件，内部使用 CloudflareOptimizedImage：

```astro
<CloudflareImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  preset="card"
  format="auto"
/>
```

### CloudflareDetailImage 组件

这是一个用于详情页的图像组件：

```astro
<CloudflareDetailImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  preset="detail"
  format="auto"
/>
```

### 预设

我们定义了几个常用的预设：

- `thumbnail`: 300x300，适合缩略图
- `card`: 500x500，适合卡片
- `detail`: 800x800，适合详情页
- `full`: 1200x1200，适合全尺寸显示

## 最佳实践

1. **使用 `format=auto`**：让 Cloudflare 自动选择最佳格式（WebP 或 AVIF）

2. **设置适当的尺寸**：根据显示需求设置适当的宽度和高度，避免加载过大的图像

3. **使用渐进式加载**：启用 `showPlaceholder` 选项，提供更好的用户体验

4. **使用响应式图像**：CloudflareOptimizedImage 组件会自动生成 srcset，支持不同设备像素比

5. **设置适当的质量**：对于大多数图像，质量设置为 80 是一个很好的平衡点

6. **使用预设**：使用预定义的预设（thumbnail、card、detail、full）来保持一致性

7. **使用 `dpr` 参数**：为高分辨率屏幕提供更清晰的图像

## 故障排除

### 图像无法加载

1. **检查 Cloudflare 图像大小调整功能是否已启用**
   - 在 Cloudflare 控制面板中，确认 Image Resizing 功能已启用

2. **检查 URL 格式**
   - 确保 URL 格式正确，参数使用逗号分隔，没有空格

3. **尝试使用基本 URL**
   - 如果优化的 URL 无法加载，尝试使用不带 `cdn-cgi/image/` 参数的基本 URL

### 图像质量问题

1. **调整质量参数**
   - 增加 `quality` 参数值，例如 `quality=90`

2. **检查格式**
   - 如果 `format=auto` 导致质量问题，尝试指定特定格式，例如 `format=webp`

3. **调整尺寸**
   - 确保图像尺寸足够大，避免过度缩小导致的质量损失

### 性能问题

1. **检查缓存状态**
   - 使用浏览器开发者工具检查图像是否被正确缓存

2. **减少图像尺寸**
   - 确保图像尺寸不超过实际需要

3. **使用适当的格式**
   - 使用 `format=auto` 让 Cloudflare 选择最佳格式

4. **启用 Polish 功能**
   - 在 Cloudflare 控制面板中启用 Polish 功能，进一步优化图像
