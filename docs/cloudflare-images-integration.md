# Cloudflare Images 集成指南

本文档介绍如何设置和使用 Cloudflare Images 服务来处理存储在 Cloudflare R2 存储桶中的原始图片文件，以创建不同尺寸的缩略图和优化图像。

## 目录

1. [设置 Cloudflare Images](#设置-cloudflare-images)
2. [配置环境变量](#配置环境变量)
3. [创建图像变体](#创建图像变体)
4. [上传图像](#上传图像)
5. [使用图像组件](#使用图像组件)
6. [渐进式加载](#渐进式加载)
7. [故障排除](#故障排除)

## 设置 Cloudflare Images

1. 登录 Cloudflare 控制面板
2. 选择你的账户和域名
3. 在左侧菜单中，点击 "Images"
4. 点击 "开始使用" 按钮
5. 按照向导完成设置

## 配置环境变量

在 `.env` 文件中添加以下环境变量：

```
# Cloudflare Images 配置
CLOUDFLARE_IMAGES_ACCOUNT_ID=你的账户ID
CLOUDFLARE_IMAGES_ACCOUNT_HASH=你的账户哈希
CLOUDFLARE_IMAGES_DELIVERY_URL=https://imagedelivery.net/你的账户哈希
```

## 创建图像变体

在 Cloudflare Images 控制面板中，创建以下变体：

1. **thumbnail** - 缩略图 (300x300)
   - 宽度：300
   - 高度：300
   - 裁剪方式：cover
   - 质量：80
   - 格式：WebP

2. **card** - 卡片 (500x500)
   - 宽度：500
   - 高度：500
   - 裁剪方式：cover
   - 质量：80
   - 格式：WebP

3. **detail** - 详情页 (800x800)
   - 宽度：800
   - 高度：800
   - 裁剪方式：contain
   - 质量：85
   - 格式：WebP

4. **full** - 全尺寸 (1200x1200)
   - 宽度：1200
   - 高度：1200
   - 裁剪方式：contain
   - 质量：90
   - 格式：WebP

5. **public** - 原始尺寸
   - 不进行任何裁剪或调整

## 上传图像

有两种方式上传图像：

### 1. 使用 Cloudflare 控制面板

1. 登录 Cloudflare 控制面板
2. 选择你的账户和域名
3. 在左侧菜单中，点击 "Images"
4. 点击 "上传" 按钮
5. 选择要上传的图像
6. 在 "自定义 ID" 字段中，输入图像 ID（格式：`assetFolder/filename` 的 Base64 编码）

### 2. 使用 API

使用 `scripts/test-cloudflare-images.js` 脚本上传图像：

```bash
node scripts/test-cloudflare-images.js
```

或者使用 API 直接上传：

```javascript
const formData = new FormData();
formData.append('file', fs.createReadStream(imagePath));
formData.append('id', imageId);

const response = await fetch(
  `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_IMAGES_ACCOUNT_ID}/images/v1`,
  {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`
    },
    body: formData
  }
);
```

## 使用图像组件

### CloudflareProgressiveImage 组件

这是一个带有渐进式加载和占位符的图像组件：

```astro
<CloudflareProgressiveImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  width={500}
  height={500}
  preset="card"
  showPlaceholder={true}
/>
```

### CloudflareImage 组件

这是一个简化的图像组件，内部使用 CloudflareProgressiveImage：

```astro
<CloudflareImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  preset="card"
/>
```

### CloudflareDetailImage 组件

这是一个用于详情页的图像组件：

```astro
<CloudflareDetailImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  preset="detail"
/>
```

## 渐进式加载

CloudflareProgressiveImage 组件支持渐进式加载，它会先显示一个模糊的占位符，然后加载高质量的图像。

```astro
<CloudflareProgressiveImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  width={500}
  height={500}
  preset="card"
  showPlaceholder={true}
  placeholderSize={20}
/>
```

## 故障排除

### 图像无法加载

1. 检查图像 ID 是否正确
2. 检查图像是否已上传到 Cloudflare Images
3. 检查变体名称是否正确
4. 检查环境变量是否正确配置

### 图像质量问题

1. 检查变体配置
2. 尝试调整质量参数
3. 尝试使用不同的格式（WebP、AVIF 等）

### 性能问题

1. 确保使用了适当的图像尺寸
2. 确保使用了适当的图像格式
3. 确保使用了渐进式加载
4. 确保使用了适当的缓存策略
