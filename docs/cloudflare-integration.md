# Cloudflare R2和Cloudflare Images集成

本文档介绍了如何将Astro项目中的静态资源从本地文件系统迁移到Cloudflare R2存储，并集成Cloudflare Images功能。

## 概述

项目中的静态资源（着色页面）包括三种类型：
1. 单色(monochrome)的PDF版本
2. 单色(monochrome)的PNG版本
3. 彩色(colored)的PNG版本

这些文件已从本地的`src/assets/coloring-pages`目录迁移到Cloudflare R2存储桶中，并通过自定义域名`static.printablecoloringhub.com`提供服务。

## 配置

### 环境变量

在项目根目录的`.env`文件中配置以下环境变量：

```
# Cloudflare R2 配置
CLOUDFLARE_R2_ACCOUNT_ID=your_account_id
CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key_id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_access_key
CLOUDFLARE_R2_BUCKET_NAME=your_bucket_name
CLOUDFLARE_R2_PUBLIC_URL=https://static.printablecoloringhub.com
```

### 文件结构

集成相关的文件：

- `src/config/cloudflare.ts` - Cloudflare配置文件
- `src/utils/cloudflareUtils.ts` - Cloudflare R2和Cloudflare Images工具函数
- `src/components/CloudflareImage.astro` - 自定义图片组件
- `src/components/CloudflareDetailImage.astro` - 自定义详情图片组件
- `src/components/CloudflareDownloadButton.astro` - 自定义下载按钮组件
- `src/components/CloudflareColoringCard.astro` - 自定义卡片组件
- `scripts/test-cloudflare-integration.js` - 测试脚本

## 使用方法

### 图片组件

使用`CloudflareImage`组件显示图片：

```astro
<CloudflareImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  preset="card"
  width={500}
  height={500}
/>
```

支持的预设：
- `thumbnail` - 缩略图 (300x300)
- `card` - 卡片 (500x500)
- `detail` - 详情页 (800x800)
- `full` - 全尺寸 (1200x1200)

### 详情图片组件

使用`CloudflareDetailImage`组件显示详情页图片：

```astro
<CloudflareDetailImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  preset="detail"
/>
```

### 下载按钮组件

使用`CloudflareDownloadButton`组件创建下载按钮：

```astro
<CloudflareDownloadButton
  assetFolder="animals/dogs/bulldog"
  format="PDF"
  fileName="bulldog-coloring-page.pdf"
/>
```

### 卡片组件

使用`CloudflareColoringCard`组件显示卡片：

```astro
<CloudflareColoringCard
  id="bulldog"
  slug="bulldog"
  title="Bulldog Coloring Page"
  assetFolder="animals/dogs/bulldog"
  categoryInfo={{
    main: "Animals",
    sub: "Dogs"
  }}
  tags={["bulldog", "dog", "pet"]}
  isFree={true}
/>
```

## 工具函数

### getR2ImageUrl

获取Cloudflare R2资源URL：

```typescript
import { getR2ImageUrl } from '../utils/cloudflareUtils';

const pdfUrl = getR2ImageUrl('animals/dogs/bulldog', 'monochrome.pdf');
const pngUrl = getR2ImageUrl('animals/dogs/bulldog', 'monochrome.png');
```

### getCloudflareImagesUrl

获取Cloudflare Images URL，支持图片变换：

```typescript
import { getCloudflareImagesUrl } from '../utils/cloudflareUtils';

const imageUrl = getCloudflareImagesUrl('animals/dogs/bulldog', 'monochrome.png', {
  width: 500,
  height: 500,
  fit: 'cover',
  quality: 80,
  format: 'webp'
});
```

## 测试

运行以下命令测试Cloudflare R2和Cloudflare Images集成：

```bash
yarn test:cloudflare
```

该测试脚本会验证URL构建是否正确，并测试HTTP请求是否成功。

## 文件命名约定

资源文件命名约定：

- 单色PDF: `assetFolder_monochrome.pdf`或`assetFolder_m.pdf`
- 单色PNG: `assetFolder_monochrome.png`或`assetFolder_m.png`
- 彩色PNG: `assetFolder_colored.png`或`assetFolder_c.png`

也支持旧的命名约定：
- 单色PDF: `monochrome.pdf`
- 单色PNG: `monochrome.png`
- 彩色PNG: `colored.png`

## 注意事项

1. 确保Cloudflare R2存储桶已正确配置，并且文件已上传。
2. 确保Cloudflare Images已配置为处理来自R2的图片。
3. 确保自定义域名`static.printablecoloringhub.com`已正确配置并指向R2存储桶。
4. 所有组件现在使用标准的`<img>`标签，不再使用Astro的`<Image />`组件。
