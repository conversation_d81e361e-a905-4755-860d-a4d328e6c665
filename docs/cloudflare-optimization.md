# Cloudflare 图像优化指南

本文档介绍如何使用 Cloudflare 的速度优化功能（Polish 和 Mirage）来优化网站图像，提高加载速度和用户体验。

## 目录

1. [Cloudflare 图像优化功能](#cloudflare-图像优化功能)
2. [设置 Cloudflare Polish](#设置-cloudflare-polish)
3. [设置 Cloudflare Mirage](#设置-cloudflare-mirage)
4. [使用优化图像组件](#使用优化图像组件)
5. [URL 参数优化](#url-参数优化)
6. [最佳实践](#最佳实践)
7. [故障排除](#故障排除)

## Cloudflare 图像优化功能

Cloudflare 提供两种主要的图像优化功能：

1. **Polish**：自动优化图像，减少文件大小，提高加载速度。
   - 支持 WebP 和 AVIF 格式转换
   - 自动调整图像质量
   - 移除不必要的元数据

2. **Mirage**：自动创建渐进式图像，提供更好的用户体验。
   - 创建低分辨率占位符
   - 支持延迟加载
   - 根据设备和网络条件优化图像

## 设置 Cloudflare Polish

1. 登录 Cloudflare 控制面板
2. 选择你的域名
3. 点击 "Speed" > "Optimization"
4. 在 "Image Optimization" 部分，启用 "Polish"
5. 选择 "Lossless" 或 "Lossy" 压缩（推荐选择 "Lossy"）
6. 启用 "WebP" 选项，允许将图像转换为 WebP 格式

## 设置 Cloudflare Mirage

1. 登录 Cloudflare 控制面板
2. 选择你的域名
3. 点击 "Speed" > "Optimization"
4. 在 "Image Optimization" 部分，启用 "Mirage"
5. 保存设置

## 使用优化图像组件

我们创建了几个组件来利用 Cloudflare 的图像优化功能：

### CloudflareOptimizedImage 组件

这是一个带有渐进式加载和占位符的图像组件：

```astro
<CloudflareOptimizedImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  width={500}
  height={500}
  preset="card"
  format="auto"
  showPlaceholder={true}
/>
```

### CloudflareImage 组件

这是一个简化的图像组件，内部使用 CloudflareOptimizedImage：

```astro
<CloudflareImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  preset="card"
  format="auto"
/>
```

### CloudflareDetailImage 组件

这是一个用于详情页的图像组件：

```astro
<CloudflareDetailImage
  assetFolder="animals/dogs/bulldog"
  filename="monochrome.png"
  alt="Bulldog Coloring Page"
  preset="detail"
  format="auto"
/>
```

## URL 参数优化

Cloudflare 支持通过 URL 参数来优化图像。我们的组件会自动添加这些参数：

| 参数 | 描述 | 示例 |
|------|------|------|
| `width` | 图像宽度 | `width=500` |
| `height` | 图像高度 | `height=500` |
| `fit` | 裁剪方式 | `fit=cover` |
| `quality` | 图像质量 | `quality=80` |
| `format` | 图像格式 | `format=auto` |

示例 URL：
```
https://static.printablecoloringhub.com/coloring-pages/animals/dogs/bulldog/monochrome.png?width=500&height=500&fit=cover&quality=80&format=auto
```

## 最佳实践

1. **使用 `format=auto`**：让 Cloudflare 自动选择最佳格式（WebP 或 AVIF）。

2. **设置适当的尺寸**：根据显示需求设置适当的宽度和高度，避免加载过大的图像。

3. **使用渐进式加载**：启用 `showPlaceholder` 选项，提供更好的用户体验。

4. **使用响应式图像**：CloudflareOptimizedImage 组件会自动生成 srcset，支持响应式图像。

5. **设置适当的质量**：对于大多数图像，质量设置为 80 是一个很好的平衡点。

6. **使用预设**：使用预定义的预设（thumbnail、card、detail、full）来保持一致性。

## 故障排除

### 图像无法加载

1. 检查图像路径是否正确
2. 确保 Cloudflare 缓存已清除
3. 尝试使用不带参数的 URL

### 图像质量问题

1. 调整质量参数
2. 尝试使用 `format=webp` 而不是 `format=auto`
3. 检查 Polish 设置是否为 "Lossy"

### 性能问题

1. 确保使用了适当的图像尺寸
2. 确保使用了渐进式加载
3. 检查网络请求，确保图像被正确缓存
