# MDX Frontmatter 字段规则文档

本文档详细说明了所有 MDX 文件的 frontmatter 字段规则，包括必需字段、格式要求和验证标准。这些规则由 `mdx-validator.js` 和 `check-and-fix-mdx.js` 脚本强制执行。

## 必需字段

以下字段在每个 MDX 文件中都是必需的：

| 字段 | 类型 | 描述 |
|------|------|------|
| `title` | 字符串 | 内容的标题 |
| `id` | 字符串 | 唯一标识符，通常与文件名匹配 |
| `assetFolder` | 字符串 | 资源文件夹名称 |
| `categoryInfo` | 对象 | 分类信息结构 |
| `description` | 字符串 | 内容的简短描述 |

## 分类信息结构 (categoryInfo)

`categoryInfo` 对象必须遵循以下规则：

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `main` | 字符串 | 是 | 主分类 |
| `sub` | 字符串 | 否 | 子分类，如果存在不能为空 |
| `subsub` | 字符串 | 否 | 子子分类，如果存在不能为空 |

**重要规则：**
- `main` 字段必须存在且不能为空
- 如果 `sub` 或 `subsub` 字段为空字符串或 `null`，应该完全删除这些字段，而不是保留空值
- 不应该包含空的 `sub` 或 `subsub` 字段

**示例：**

正确的结构：
```yaml
categoryInfo:
  main: Animals
  sub: Dogs
```

或者：
```yaml
categoryInfo:
  main: Animals
  sub: Dogs
  subsub: Puppies
```

错误的结构：
```yaml
categoryInfo:
  main: Animals
  sub: ""
  subsub: null
```

## 数组字段

以下字段必须是数组：

| 字段 | 类型 | 描述 |
|------|------|------|
| `tags` | 字符串数组 | 与内容相关的标签 |
| `collections` | 字符串数组 | 内容所属的集合 |

**tags 特殊规则：**
- `tags` 数组中的项目必须是字符串
- `tags` 数组中不能包含纯数字（如 "24"、"123" 等）
- 如果过滤掉纯数字后 `tags` 为空，将添加一个基于 `title` 的默认标签

**示例：**

正确的 tags：
```yaml
tags:
  - kobe bryant
  - basketball
  - lakers
  - sports
```

错误的 tags：
```yaml
tags:
  - kobe bryant
  - basketball
  - "24"  # 纯数字标签不允许
  - lakers
```

## 布尔字段

以下字段必须是布尔值（true 或 false）：

| 字段 | 类型 | 描述 |
|------|------|------|
| `featured` | 布尔值 | 是否为特色内容 |
| `premium` | 布尔值 | 是否为高级内容 |
| `popular` | 布尔值 | 是否为热门内容 |

**示例：**
```yaml
featured: true
premium: false
popular: true
```

## 日期字段

| 字段 | 类型 | 描述 |
|------|------|------|
| `dateAdded` | 日期字符串 | 添加日期，格式为 ISO 8601 |

**示例：**
```yaml
dateAdded: 2025-05-19T23:41:32.711Z
```

## 完整示例

以下是一个符合所有规则的完整 frontmatter 示例：

```yaml
---
title: Adorable Fluffy Puppy
id: Adorable-Fluffy-Puppy
assetFolder: adorable-fluffy-puppy
description: A cute and fluffy puppy with big eyes and a playful expression, perfect for animal lovers of all ages.
categoryInfo:
  main: Animals
  sub: Dogs
  subsub: Puppies
collections:
  - Cute Animals
  - Popular Pets
tags:
  - puppy
  - dog
  - fluffy
  - cute
  - animals
  - pets
  - coloring page
  - printable
  - free
popular: true
featured: true
premium: false
dateAdded: 2025-05-19T23:41:32.711Z
---
```

## 内容格式规则

除了 frontmatter 字段外，MDX 内容部分也有一些格式规则：

### 数字列表格式

数字列表必须使用 `1. ` 格式（数字后跟一个点和一个空格），而不是 `1)` 或其他格式。

**正确格式：**
```markdown
1. 第一项
1. 第二项
1. 第三项
```

**错误格式：**
```markdown
1) 第一项
1) 第二项
1) 第三项
```

## 验证和修复

使用以下命令验证和修复 MDX 文件：

- `yarn check-mdx` - 检查所有 MDX 文件是否符合规则
- `yarn fix-mdx` - 修复所有不符合规则的 MDX 文件

这些命令使用 `src/scripts/mdx-validator.js` 脚本执行验证和修复操作。
