/**
 * Cloudflare R2资源回退脚本
 *
 * 该脚本用于处理Cloudflare R2资源的加载失败情况，
 * 当资源加载失败时，会尝试加载其他可能的URL。
 */

// Cloudflare R2配置
const CLOUDFLARE_R2_PUBLIC_URL = 'https://static.printablecoloringhub.com';

document.addEventListener('DOMContentLoaded', () => {
  // 处理下载按钮
  setupDownloadButtons();

  // 处理图片加载
  setupImageFallback();

  // 监听DOM变化，处理新添加的图片
  observeDOMChanges();
});

/**
 * 设置下载按钮的回退机制
 */
function setupDownloadButtons() {
  const downloadButtons = document.querySelectorAll('[data-multiple-urls]');

  downloadButtons.forEach(button => {
    // 获取文件类型
    const fileType = button.getAttribute('data-file-type');

    // 获取文件名
    let fileName;
    if (button.hasAttribute('data-download-filename')) {
      // 优先使用data-download-filename属性
      fileName = button.getAttribute('data-download-filename');
    } else if (button.hasAttribute('download')) {
      // 其次使用download属性
      fileName = button.getAttribute('download');
    } else {
      // 最后使用默认文件名
      fileName = `coloring-page.${fileType || 'pdf'}`;
      button.setAttribute('download', fileName);
    }

    // 点击下载
    button.addEventListener('click', function(e) {
      // 阻止默认行为
      e.preventDefault();

      // 如果按钮已经处于下载状态，则不执行任何操作
      if (this.hasAttribute('data-downloading') && this.getAttribute('data-downloading') === 'true') {
        return;
      }

      // 获取所有可能的URL
      const urlsStr = this.getAttribute('data-urls');
      if (!urlsStr) {
        console.error('No data-urls attribute found on download button');
        return;
      }

      const urls = urlsStr.split(',').filter(url => url.trim() !== '');
      if (urls.length === 0) {
        console.error('No valid URLs found in data-urls attribute');
        return;
      }

      // 获取文件名
      let downloadFileName;
      if (this.hasAttribute('data-download-filename')) {
        downloadFileName = this.getAttribute('data-download-filename');
      } else if (this.hasAttribute('download')) {
        downloadFileName = this.getAttribute('download');
      } else {
        downloadFileName = fileName;
      }

      // 保存原始按钮文本
      const originalText = this.innerHTML;
      this.setAttribute('data-original-text', originalText);

      // 设置按钮为下载中状态
      this.setAttribute('data-downloading', 'true');
      this.classList.add('opacity-70', 'cursor-not-allowed');

      // 更改按钮文本为"Downloading..."
      const buttonFormat = fileType ? fileType.toUpperCase() : 'FILE';
      this.innerHTML = `<span class="mr-2">
        <svg class="w-6 h-6 animate-pulse" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-7Zm.394 9.553a1 1 0 0 0-1.817.062l-2.5 6A1 1 0 0 0 8 19h8a1 1 0 0 0 .894-1.447l-2-4A1 1 0 0 0 13.2 13.4l-.53.706-1.276-2.553ZM13 9.5a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z" clip-rule="evenodd"/>
        </svg>
      </span>
      Downloading`;

      // 尝试下载第一个URL
      tryDownload(urls, 0, downloadFileName || 'download', this);
    });
  });
}

/**
 * 尝试下载指定URL
 * @param {string[]} urls - 可能的URL数组
 * @param {number} index - 当前尝试的URL索引
 * @param {string} fileName - 下载文件名
 * @param {HTMLElement} button - 下载按钮元素
 */
function tryDownload(urls, index, fileName, button) {
  if (index >= urls.length) {
    // 所有URL都尝试过了，但都失败了
    console.error('All download URLs failed');
    alert('Unable to download file. Please try again later.');

    // 恢复按钮状态
    resetButtonState(button);
    return;
  }

  const url = urls[index].trim();
  if (!url) {
    // 跳过空URL
    tryDownload(urls, index + 1, fileName, button);
    return;
  }

  console.log(`尝试下载URL: ${url}`);

  // 直接尝试下载
  executeDownload(url, fileName, button);
}

/**
 * 恢复按钮状态
 * @param {HTMLElement} button - 下载按钮元素
 */
function resetButtonState(button) {
  if (!button) return;

  // 恢复原始文本
  if (button.hasAttribute('data-original-text')) {
    button.innerHTML = button.getAttribute('data-original-text');
  }

  // 移除下载中状态
  button.removeAttribute('data-downloading');
  button.classList.remove('opacity-70', 'cursor-not-allowed');
}

/**
 * 执行下载操作
 * @param {string} url - 下载URL
 * @param {string} fileName - 文件名
 * @param {HTMLElement} button - 下载按钮元素
 */
function executeDownload(url, fileName, button) {
  try {
    console.log(`执行下载: ${url}`);

    // 检查文件类型
    const fileExtension = fileName.split('.').pop().toLowerCase();

    // 对PNG文件使用特殊处理
    if (fileExtension === 'png') {
      // 使用Fetch API获取文件内容，然后创建Blob并下载
      fetch(url)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.blob();
        })
        .then(blob => {
          // 创建一个新的Blob对象，强制设置MIME类型为application/octet-stream
          // 这会让浏览器将其视为下载而不是显示
          const forcedBlob = new Blob([blob], { type: 'application/octet-stream' });

          // 创建一个URL对象
          const blobUrl = URL.createObjectURL(forcedBlob);

          // 创建一个隐藏的a元素
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = blobUrl;
          a.setAttribute('download', fileName);
          document.body.appendChild(a);

          // 触发点击
          a.click();

          // 清理
          setTimeout(() => {
            document.body.removeChild(a);
            // 释放URL对象
            URL.revokeObjectURL(blobUrl);

            // 恢复按钮状态 - 下载完成后延迟1秒恢复，让用户有时间看到下载状态
            setTimeout(() => {
              resetButtonState(button);
            }, 1000);
          }, 100);
        })
        .catch(error => {
          console.error('PNG下载失败:', error);
          alert('Download failed. Please try again later.');

          // 恢复按钮状态
          resetButtonState(button);
        });
    } else {
      // 对其他文件类型使用标准方法
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.setAttribute('download', fileName);
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);

        // 恢复按钮状态 - 下载完成后延迟1秒恢复，让用户有时间看到下载状态
        setTimeout(() => {
          resetButtonState(button);
        }, 1000);
      }, 100);
    }
  } catch (error) {
    console.error('下载失败:', error);
    alert('Download failed. Please try again later.');

    // 恢复按钮状态
    resetButtonState(button);
  }
}



/**
 * 设置图片加载的回退机制
 */
function setupImageFallback() {
  // 处理所有图片
  const allImages = document.querySelectorAll('img[src]');
  allImages.forEach(img => {
    // 处理逗号分隔的URL
    handleCommaSeparatedUrls(img);

    // 添加错误处理
    addErrorHandler(img);
  });
}

/**
 * 检查URL是否有效
 * @param {string} url - 要检查的URL
 * @returns {boolean} - URL是否有效
 */
function isValidUrl(url) {
  if (!url || url.trim() === '') return false;

  // 检查是否是不完整的URL
  if (url.includes('/cdn-cgi/image/') && !url.includes('/coloring-pages/')) return false;

  // 检查是否只是URL参数片段
  if (url.match(/^(width|height|fit|quality|format|dpr|gravity)=/)) return false;

  // 检查是否是相对路径但不是完整的资源路径
  if (url.startsWith('/') && !url.includes('/coloring-pages/') && !url.startsWith('/_image')) return false;

  // 允许Astro开发服务器的图像优化URL
  if (url.startsWith('/_image') || url.includes('/@fs/')) return true;

  // 允许本地图像URL
  if (url.startsWith('/images/')) return true;

  // 允许SVG图像
  if (url.endsWith('.svg')) return true;

  return true;
}

/**
 * 处理逗号分隔的URL
 * @param {HTMLImageElement} img - 图片元素
 */
function handleCommaSeparatedUrls(img) {
  const src = img.getAttribute('src');

  // 检查URL是否有效
  if (src) {
    // 跳过Astro开发服务器的图像优化URL
    if (src.startsWith('/_image') || src.includes('/@fs/')) {
      return;
    }

    // 检查是否是不完整的URL
    if (!isValidUrl(src)) {
      console.error('Incomplete URL detected:', src);
      showPlaceholder(img);
      return;
    }

    // 处理逗号分隔的URL
    if (src.includes(',')) {
      // 分割URL
      const urls = src.split(',').filter(url => isValidUrl(url));

      if (urls.length > 0) {
        // 使用第一个URL
        img.setAttribute('src', urls[0]);
        // 保存其余URL作为备用
        if (urls.length > 1) {
          img.setAttribute('data-fallback-urls', urls.slice(1).join(','));
        }
      } else {
        // 如果没有有效的URL，显示占位符
        showPlaceholder(img);
      }
    }
  }

  // 处理data-fallback-urls属性中的逗号分隔URL
  const fallbackUrls = img.getAttribute('data-fallback-urls');
  if (fallbackUrls) {
    const urls = fallbackUrls.split(',').filter(url => isValidUrl(url));

    if (urls.length > 0) {
      img.setAttribute('data-fallback-urls', urls.join(','));
    } else {
      // 如果没有有效的备用URL，移除属性
      img.removeAttribute('data-fallback-urls');
    }
  }
}

/**
 * 添加错误处理
 * @param {HTMLImageElement} img - 图片元素
 */
function addErrorHandler(img) {
  // 防止重复添加事件监听器
  if (img.hasAttribute('data-error-handler-added')) return;
  img.setAttribute('data-error-handler-added', 'true');

  img.addEventListener('error', function() {
    // 获取当前src
    const src = this.getAttribute('src');
    if (!src) return;

    // 跳过Astro开发服务器的图像优化URL
    if (src.startsWith('/_image') || src.includes('/@fs/')) {
      return;
    }

    // 检查URL是否有效
    if (!isValidUrl(src)) {
      console.error('Invalid URL detected in error handler:', src);
      showPlaceholder(this);
      return;
    }

    // 如果图片加载失败，首先尝试使用不带cdn-cgi参数的URL
    if (src.includes('/cdn-cgi/image/')) {
      // 提取完整的资源路径
      const match = src.match(/\/cdn-cgi\/image\/[^/]+\/(coloring-pages\/.+)$/);
      if (match && match[1]) {
        const resourcePath = match[1];
        const baseUrl = `${CLOUDFLARE_R2_PUBLIC_URL}/${resourcePath}`;
        console.log('Image load failed, trying without cdn-cgi:', baseUrl);
        this.setAttribute('src', baseUrl);
        this.removeAttribute('srcset');
      } else {
        // 无法提取资源路径，显示占位符
        console.error('Cannot extract resource path from URL:', src);
        showPlaceholder(this);
      }
      return;
    }

    // 检查是否有备用URL
    const fallbackUrls = this.getAttribute('data-fallback-urls');
    if (fallbackUrls) {
      const urls = fallbackUrls.split(',').filter(url => url.trim() !== '');
      if (urls.length > 0) {
        console.log(`Image load failed for ${src}, trying fallback URL: ${urls[0]}`);
        // 使用第一个备用URL
        this.setAttribute('src', urls[0]);
        // 更新备用URL列表
        if (urls.length > 1) {
          this.setAttribute('data-fallback-urls', urls.slice(1).join(','));
        } else {
          this.removeAttribute('data-fallback-urls');
        }
        return;
      }
    }

    // 如果没有备用URL或所有URL都失败，显示占位符
    console.log(`No more fallback URLs for image, showing placeholder`);
    showPlaceholder(this);
  });
}

/**
 * 显示占位符图片
 * @param {HTMLImageElement} img - 图片元素
 */
function showPlaceholder(img) {
  // 使用Cloudflare R2上的占位符图片
  img.src = 'https://static.printablecoloringhub.com/images/placeholder.svg'; // 使用SVG占位符
  img.alt = 'Image not available';
  img.classList.add('image-not-found');

  // 添加占位符样式
  img.style.backgroundColor = '#f0f0f0';
  img.style.objectFit = 'contain';
  img.style.padding = '10px';
}

/**
 * 监听DOM变化，处理新添加的图片
 */
function observeDOMChanges() {
  // 创建一个节流函数，限制处理频率
  let processingNodes = false;
  const processQueue = [];
  let processTimer = null;

  function processNodes() {
    if (processingNodes || processQueue.length === 0) return;

    processingNodes = true;

    // 每次处理最多5个节点
    const nodesToProcess = processQueue.splice(0, 5);

    nodesToProcess.forEach(node => {
      try {
        // 如果是图片元素
        if (node.tagName === 'IMG') {
          // 检查src属性是否有效
          const src = node.getAttribute('src');
          if (src && isValidUrl(src)) {
            handleCommaSeparatedUrls(node);
            addErrorHandler(node);
          } else if (src) {
            // 如果src无效，显示占位符
            showPlaceholder(node);
          }
        }
        // 如果包含子元素，查找其中的图片
        else if (node.querySelectorAll) {
          const images = node.querySelectorAll('img[src]:not([data-error-handler-added])');
          if (images.length <= 3) { // 限制每次处理的图片数量
            images.forEach(img => {
              // 检查src属性是否有效
              const src = img.getAttribute('src');
              if (src && isValidUrl(src)) {
                handleCommaSeparatedUrls(img);
                addErrorHandler(img);
              } else if (src) {
                // 如果src无效，显示占位符
                showPlaceholder(img);
              }
            });
          } else {
            // 如果图片太多，分批处理
            for (let i = 0; i < 3; i++) {
              if (images[i]) {
                const src = images[i].getAttribute('src');
                if (src && isValidUrl(src)) {
                  handleCommaSeparatedUrls(images[i]);
                  addErrorHandler(images[i]);
                } else if (src) {
                  showPlaceholder(images[i]);
                }
              }
            }
            // 将剩余的图片添加到队列中
            if (images.length > 3) {
              for (let i = 3; i < images.length; i++) {
                if (images[i]) processQueue.push(images[i]);
              }
            }
          }
        }
      } catch (e) {
        console.error('Error processing node:', e);
      }
    });

    processingNodes = false;

    // 如果队列中还有节点，继续处理
    if (processQueue.length > 0) {
      processTimer = setTimeout(processNodes, 100);
    }
  }

  // 创建MutationObserver
  const observer = new MutationObserver(mutations => {
    // 收集所有新添加的节点
    let hasNewNodes = false;

    mutations.forEach(mutation => {
      if (mutation.addedNodes && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            processQueue.push(node);
            hasNewNodes = true;
          }
        });
      }
    });

    // 开始处理节点
    if (hasNewNodes && !processingNodes) {
      // 清除之前的定时器
      if (processTimer) clearTimeout(processTimer);
      processTimer = setTimeout(processNodes, 100);
    }
  });

  // 配置观察选项
  const config = { childList: true, subtree: true };

  // 开始观察document.body
  observer.observe(document.body, config);

  // 处理已有的图片，但限制数量
  const existingImages = document.querySelectorAll('img[src]:not([data-error-handler-added])');
  let processedCount = 0;

  // 先处理前10个图片
  for (let i = 0; i < Math.min(10, existingImages.length); i++) {
    const img = existingImages[i];
    const src = img.getAttribute('src');

    // 跳过Astro开发服务器的图像优化URL
    if (src && (src.startsWith('/_image') || src.includes('/@fs/'))) {
      continue;
    }

    if (src && isValidUrl(src)) {
      handleCommaSeparatedUrls(img);
      addErrorHandler(img);
      processedCount++;
    } else if (src) {
      showPlaceholder(img);
      processedCount++;
    }
  }

  // 将剩余的图片添加到队列中
  if (existingImages.length > 10) {
    for (let i = 10; i < existingImages.length; i++) {
      processQueue.push(existingImages[i]);
    }
    // 开始处理队列
    processTimer = setTimeout(processNodes, 100);
  }
}
