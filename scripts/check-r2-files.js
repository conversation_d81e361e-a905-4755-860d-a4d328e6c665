/**
 * 检查R2文件脚本
 * 
 * 该脚本用于检查R2存储中的文件是否可以正确访问。
 * 它会尝试访问R2存储中的文件，并检查是否可以成功获取。
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 加载环境变量
dotenv.config();

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Cloudflare R2配置
const CLOUDFLARE_R2_PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL || 'https://static.printablecoloringhub.com';
const CLOUDFLARE_R2_BASE_PATH = 'coloring-pages';

// 测试资源文件夹
const TEST_ASSET_FOLDERS = [
  'grogu_in_pram_best',
  'grogu_sipping_cup',
  'grogu_standing_best',
  'Pikachu-Medieval-Adventure',
  'Ash-Pikachu-Pokemon-Adventure'
];

// 文件类型
const FILE_TYPES = [
  'monochrome.png',
  'monochrome.pdf',
  'colored.png'
];

/**
 * 构建R2 URL
 * @param {string} assetFolder - 资源文件夹名称
 * @param {string} filename - 文件名
 * @returns {string} - 完整的R2 URL
 */
function buildR2Url(assetFolder, filename) {
  return `${CLOUDFLARE_R2_PUBLIC_URL}/${CLOUDFLARE_R2_BASE_PATH}/${assetFolder}/${filename}`;
}

/**
 * 测试URL是否可访问
 * @param {string} url - 要测试的URL
 * @returns {Promise<Object>} - 测试结果
 */
async function testUrl(url) {
  try {
    const response = await fetch(url, { 
      method: 'GET',
      headers: {
        'Accept': 'image/png,image/jpeg,image/webp,application/pdf,*/*'
      }
    });
    
    const status = response.status;
    const statusText = response.statusText;
    const contentType = response.headers.get('content-type');
    
    return {
      isAccessible: response.ok,
      status,
      statusText,
      contentType,
      url
    };
  } catch (error) {
    console.error(`Error testing URL ${url}:`, error.message);
    return {
      isAccessible: false,
      error: error.message,
      url
    };
  }
}

/**
 * 测试资源文件夹中的文件
 * @param {string} assetFolder - 资源文件夹名称
 * @returns {Promise<Object>} - 测试结果
 */
async function testAssetFolder(assetFolder) {
  const results = {};
  
  for (const fileType of FILE_TYPES) {
    const url = buildR2Url(assetFolder, fileType);
    const result = await testUrl(url);
    
    results[fileType] = result;
  }
  
  return results;
}

/**
 * 主函数
 */
async function main() {
  console.log('开始检查R2文件...');
  console.log(`Cloudflare R2公共URL: ${CLOUDFLARE_R2_PUBLIC_URL}`);
  console.log(`Cloudflare R2基本路径: ${CLOUDFLARE_R2_BASE_PATH}`);
  console.log('-----------------------------------');
  
  const allResults = {};
  
  for (const assetFolder of TEST_ASSET_FOLDERS) {
    console.log(`测试资源文件夹: ${assetFolder}`);
    allResults[assetFolder] = await testAssetFolder(assetFolder);
    
    // 打印结果
    for (const [fileType, result] of Object.entries(allResults[assetFolder])) {
      console.log(`  文件类型: ${fileType}`);
      console.log(`    URL: ${result.url}`);
      console.log(`    可访问: ${result.isAccessible}`);
      
      if (result.isAccessible) {
        console.log(`    状态: ${result.status} ${result.statusText}`);
        console.log(`    内容类型: ${result.contentType}`);
      } else if (result.error) {
        console.log(`    错误: ${result.error}`);
      } else {
        console.log(`    状态: ${result.status} ${result.statusText}`);
      }
    }
    
    console.log('-----------------------------------');
  }
  
  // 保存结果到文件
  const resultsPath = path.join(__dirname, 'r2-files-check-results.json');
  fs.writeFileSync(resultsPath, JSON.stringify(allResults, null, 2));
  console.log(`测试结果已保存到: ${resultsPath}`);
}

main().catch(error => {
  console.error('测试过程中出错:', error);
  process.exit(1);
});
