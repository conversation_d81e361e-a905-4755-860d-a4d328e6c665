[2025-05-23T22:21:10.123Z] 开始处理涂色页资源...
[2025-05-23T22:21:10.127Z] 源目录: /Users/<USER>/printablecoloringhub/scripts/resource
[2025-05-23T22:21:10.127Z] MDX 目标目录: /Users/<USER>/printablecoloringhub/src/content/coloring-pages
[2025-05-23T22:21:10.127Z] R2 存储桶: uslocal
[2025-05-23T22:21:10.127Z] R2 基础路径: coloring-pages
[2025-05-23T22:21:10.127Z] 模拟执行: 否
[2025-05-23T22:21:10.127Z] 强制重新上传: 否
[2025-05-23T22:21:10.127Z] -----------------------------------
[2025-05-23T22:21:10.128Z] 已加载上传记录，包含 3 个文件
[2025-05-23T22:21:10.128Z] 找到 1 个涂色页目录
[2025-05-23T22:21:10.128Z] 处理涂色页目录: majestic-fairy-tale-castle
[2025-05-23T22:21:10.131Z] MDX 文件验证通过: majestic-fairy-tale-castle.mdx
[2025-05-23T22:21:10.131Z] 成功复制 MDX 文件到: /Users/<USER>/printablecoloringhub/src/content/coloring-pages/majestic-fairy-tale-castle.mdx
[2025-05-23T22:21:10.131Z] 跳过已上传的文件 (本地记录): coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.png
[2025-05-23T22:21:10.132Z] 跳过已上传的文件 (本地记录): coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.pdf
[2025-05-23T22:21:10.132Z] 跳过已上传的文件 (本地记录): coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_colored.png
[2025-05-23T22:21:10.132Z] 资源文件处理完成: 0 个文件已上传, 3 个文件已跳过, 共 3 个文件
[2025-05-23T22:21:10.132Z] 已保存上传记录，包含 3 个文件
[2025-05-23T22:21:10.132Z] -----------------------------------
[2025-05-23T22:21:10.132Z] 处理完成! 1/1 个涂色页成功处理
[2025-05-23T22:21:10.132Z] 已保存上传记录，包含 3 个文件
[2025-05-23T23:47:43.590Z] 已加载 3 个已处理资源的记录
[2025-05-23T23:47:43.595Z] 开始同步资源文件...
[2025-05-23T23:47:43.595Z] 配置: {
  "resourceDir": "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages",
  "contentDir": "/Users/<USER>/printablecoloringhub/src/content/coloring-pages",
  "skipProcessed": true,
  "dryRun": true
}
[2025-05-23T23:47:43.595Z] 找到 1 个资源目录
[2025-05-23T23:47:43.595Z] 处理资源目录: majestic-fairy-tale-castle
[2025-05-23T23:47:43.598Z] [DRY RUN] 跳过移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/majestic-fairy-tale-castle.mdx
[2025-05-23T23:47:43.598Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"majestic-fairy-tale-castle_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:47:43.599Z] [DRY RUN] 跳过执行 put-object 命令: coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_colored.png
[2025-05-23T23:47:43.599Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"majestic-fairy-tale-castle_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:47:43.599Z] [DRY RUN] 跳过执行 put-object 命令: coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.pdf
[2025-05-23T23:47:43.599Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"majestic-fairy-tale-castle_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:47:43.599Z] [DRY RUN] 跳过执行 put-object 命令: coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.png
[2025-05-23T23:47:43.599Z] 成功处理资源目录: majestic-fairy-tale-castle
[2025-05-23T23:47:43.599Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-23T23:47:43.600Z] 已保存 3 个已处理资源的记录
[2025-05-23T23:47:43.600Z] 资源同步完成
[2025-05-23T23:47:43.600Z] 总结: 总计 1 个资源目录, 成功 1, 跳过 0, 失败 0
[2025-05-23T23:48:02.325Z] 已加载 3 个已处理资源的记录
[2025-05-23T23:48:02.330Z] 开始同步资源文件...
[2025-05-23T23:48:02.330Z] 配置: {
  "resourceDir": "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages",
  "contentDir": "/Users/<USER>/printablecoloringhub/src/content/coloring-pages",
  "skipProcessed": true,
  "dryRun": false
}
[2025-05-23T23:48:02.330Z] 找到 1 个资源目录
[2025-05-23T23:48:02.330Z] 处理资源目录: majestic-fairy-tale-castle
[2025-05-23T23:48:02.334Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/majestic-fairy-tale-castle.mdx
[2025-05-23T23:48:02.334Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"majestic-fairy-tale-castle_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:48:04.660Z] 成功上传文件: coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_colored.png
[2025-05-23T23:48:04.661Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"majestic-fairy-tale-castle_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:48:06.572Z] 成功上传文件: coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.pdf
[2025-05-23T23:48:06.572Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"majestic-fairy-tale-castle_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:48:08.838Z] 成功上传文件: coloring-pages/majestic-fairy-tale-castle/majestic-fairy-tale-castle_monochrome.png
[2025-05-23T23:48:08.838Z] 成功处理资源目录: majestic-fairy-tale-castle
[2025-05-23T23:48:08.838Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-23T23:48:08.839Z] 已保存 4 个已处理资源的记录
[2025-05-23T23:48:08.839Z] 资源同步完成
[2025-05-23T23:48:08.839Z] 总结: 总计 1 个资源目录, 成功 1, 跳过 0, 失败 0
[2025-05-23T23:48:16.399Z] 已加载 4 个已处理资源的记录
[2025-05-23T23:48:16.404Z] 开始同步资源文件...
[2025-05-23T23:48:16.404Z] 配置: {
  "resourceDir": "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages",
  "contentDir": "/Users/<USER>/printablecoloringhub/src/content/coloring-pages",
  "skipProcessed": true,
  "dryRun": false
}
[2025-05-23T23:48:16.404Z] 找到 1 个资源目录
[2025-05-23T23:48:16.404Z] 处理资源目录: majestic-fairy-tale-castle
[2025-05-23T23:48:16.404Z] 跳过已完全处理的资源目录: majestic-fairy-tale-castle
[2025-05-23T23:48:16.405Z] 跳过已处理的资源目录: majestic-fairy-tale-castle
[2025-05-23T23:48:16.405Z] 已保存 4 个已处理资源的记录
[2025-05-23T23:48:16.405Z] 资源同步完成
[2025-05-23T23:48:16.405Z] 总结: 总计 1 个资源目录, 成功 0, 跳过 1, 失败 0
[2025-05-23T23:48:59.482Z] 已加载 4 个已处理资源的记录
[2025-05-23T23:48:59.487Z] 开始同步资源文件...
[2025-05-23T23:48:59.487Z] 配置: {
  "resourceDir": "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages",
  "contentDir": "/Users/<USER>/printablecoloringhub/src/content/coloring-pages",
  "skipProcessed": true,
  "dryRun": false
}
[2025-05-23T23:48:59.487Z] 找到 6 个资源目录
[2025-05-23T23:48:59.487Z] 处理资源目录: family-barbecue-fun-time
[2025-05-23T23:48:59.490Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/family-barbecue-fun-time.mdx
[2025-05-23T23:48:59.490Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"family-barbecue-fun-time_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:02.416Z] 成功上传文件: coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_colored.png
[2025-05-23T23:49:02.417Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"family-barbecue-fun-time_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:04.612Z] 成功上传文件: coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_monochrome.pdf
[2025-05-23T23:49:04.613Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"family-barbecue-fun-time_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:07.197Z] 成功上传文件: coloring-pages/family-barbecue-fun-time/family-barbecue-fun-time_monochrome.png
[2025-05-23T23:49:07.197Z] 成功处理资源目录: family-barbecue-fun-time
[2025-05-23T23:49:07.197Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-23T23:49:07.198Z] 处理资源目录: little-gardeners-sunny-day
[2025-05-23T23:49:07.199Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/little-gardeners-sunny-day.mdx
[2025-05-23T23:49:07.199Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"little-gardeners-sunny-day_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:09.670Z] 成功上传文件: coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_colored.png
[2025-05-23T23:49:09.670Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"little-gardeners-sunny-day_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:11.783Z] 成功上传文件: coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_monochrome.pdf
[2025-05-23T23:49:11.784Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"little-gardeners-sunny-day_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:14.608Z] 成功上传文件: coloring-pages/little-gardeners-sunny-day/little-gardeners-sunny-day_monochrome.png
[2025-05-23T23:49:14.608Z] 成功处理资源目录: little-gardeners-sunny-day
[2025-05-23T23:49:14.609Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-23T23:49:14.609Z] 处理资源目录: majestic-fairy-tale-castle
[2025-05-23T23:49:14.609Z] 跳过已完全处理的资源目录: majestic-fairy-tale-castle
[2025-05-23T23:49:14.609Z] 跳过已处理的资源目录: majestic-fairy-tale-castle
[2025-05-23T23:49:14.609Z] 处理资源目录: patriotic-backyard-celebration
[2025-05-23T23:49:14.610Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/patriotic-backyard-celebration.mdx
[2025-05-23T23:49:14.610Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"patriotic-backyard-celebration_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:17.352Z] 成功上传文件: coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_colored.png
[2025-05-23T23:49:17.352Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"patriotic-backyard-celebration_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:19.542Z] 成功上传文件: coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_monochrome.pdf
[2025-05-23T23:49:19.543Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"patriotic-backyard-celebration_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:22.079Z] 成功上传文件: coloring-pages/patriotic-backyard-celebration/patriotic-backyard-celebration_monochrome.png
[2025-05-23T23:49:22.079Z] 成功处理资源目录: patriotic-backyard-celebration
[2025-05-23T23:49:22.079Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-23T23:49:22.080Z] 处理资源目录: route-sixty-six-adventure
[2025-05-23T23:49:22.080Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/route-sixty-six-adventure.mdx
[2025-05-23T23:49:22.080Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"route-sixty-six-adventure_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:24.354Z] 成功上传文件: coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_colored.png
[2025-05-23T23:49:24.355Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"route-sixty-six-adventure_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:26.503Z] 成功上传文件: coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_monochrome.pdf
[2025-05-23T23:49:26.504Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"route-sixty-six-adventure_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:29.351Z] 成功上传文件: coloring-pages/route-sixty-six-adventure/route-sixty-six-adventure_monochrome.png
[2025-05-23T23:49:29.351Z] 成功处理资源目录: route-sixty-six-adventure
[2025-05-23T23:49:29.351Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-23T23:49:29.351Z] 已保存 8 个已处理资源的记录
[2025-05-23T23:49:29.351Z] 处理资源目录: santorini-snapshot-serenity
[2025-05-23T23:49:29.353Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/santorini-snapshot-serenity.mdx
[2025-05-23T23:49:29.354Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"santorini-snapshot-serenity_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:31.963Z] 成功上传文件: coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_colored.png
[2025-05-23T23:49:31.963Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"santorini-snapshot-serenity_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:34.091Z] 成功上传文件: coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_monochrome.pdf
[2025-05-23T23:49:34.092Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"santorini-snapshot-serenity_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-23T23:49:36.437Z] 成功上传文件: coloring-pages/santorini-snapshot-serenity/santorini-snapshot-serenity_monochrome.png
[2025-05-23T23:49:36.437Z] 成功处理资源目录: santorini-snapshot-serenity
[2025-05-23T23:49:36.437Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-23T23:49:36.438Z] 已保存 9 个已处理资源的记录
[2025-05-23T23:49:36.438Z] 资源同步完成
[2025-05-23T23:49:36.438Z] 总结: 总计 6 个资源目录, 成功 5, 跳过 1, 失败 0
[2025-05-23T23:51:07.695Z] 已加载 9 个已处理资源的记录
[2025-05-23T23:51:07.700Z] 开始同步资源文件...
[2025-05-23T23:51:07.700Z] 配置: {
  "resourceDir": "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages",
  "contentDir": "/Users/<USER>/printablecoloringhub/src/content/coloring-pages",
  "skipProcessed": true,
  "dryRun": false
}
[2025-05-23T23:51:07.700Z] 找到 6 个资源目录
[2025-05-23T23:51:07.700Z] 处理资源目录: family-barbecue-fun-time
[2025-05-23T23:51:07.700Z] 跳过已完全处理的资源目录: family-barbecue-fun-time
[2025-05-23T23:51:07.700Z] 跳过已处理的资源目录: family-barbecue-fun-time
[2025-05-23T23:51:07.701Z] 处理资源目录: little-gardeners-sunny-day
[2025-05-23T23:51:07.701Z] 跳过已完全处理的资源目录: little-gardeners-sunny-day
[2025-05-23T23:51:07.701Z] 跳过已处理的资源目录: little-gardeners-sunny-day
[2025-05-23T23:51:07.701Z] 处理资源目录: majestic-fairy-tale-castle
[2025-05-23T23:51:07.701Z] 跳过已完全处理的资源目录: majestic-fairy-tale-castle
[2025-05-23T23:51:07.701Z] 跳过已处理的资源目录: majestic-fairy-tale-castle
[2025-05-23T23:51:07.701Z] 处理资源目录: patriotic-backyard-celebration
[2025-05-23T23:51:07.701Z] 跳过已完全处理的资源目录: patriotic-backyard-celebration
[2025-05-23T23:51:07.701Z] 跳过已处理的资源目录: patriotic-backyard-celebration
[2025-05-23T23:51:07.701Z] 处理资源目录: route-sixty-six-adventure
[2025-05-23T23:51:07.701Z] 跳过已完全处理的资源目录: route-sixty-six-adventure
[2025-05-23T23:51:07.702Z] 跳过已处理的资源目录: route-sixty-six-adventure
[2025-05-23T23:51:07.702Z] 已保存 9 个已处理资源的记录
[2025-05-23T23:51:07.702Z] 处理资源目录: santorini-snapshot-serenity
[2025-05-23T23:51:07.702Z] 跳过已完全处理的资源目录: santorini-snapshot-serenity
[2025-05-23T23:51:07.702Z] 跳过已处理的资源目录: santorini-snapshot-serenity
[2025-05-23T23:51:07.702Z] 已保存 9 个已处理资源的记录
[2025-05-23T23:51:07.702Z] 资源同步完成
[2025-05-23T23:51:07.702Z] 总结: 总计 6 个资源目录, 成功 0, 跳过 6, 失败 0
[2025-05-23T23:53:49.007Z] 已加载 9 个已处理资源的记录
[2025-05-23T23:53:49.011Z] 开始同步资源文件...
[2025-05-23T23:53:49.012Z] 配置: {
  "resourceDir": "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages",
  "contentDir": "/Users/<USER>/printablecoloringhub/src/content/coloring-pages",
  "skipProcessed": true,
  "dryRun": false
}
[2025-05-23T23:53:49.012Z] 找到 6 个资源目录
[2025-05-23T23:53:49.012Z] 处理资源目录: family-barbecue-fun-time
[2025-05-23T23:53:49.012Z] 跳过已完全处理的资源目录: family-barbecue-fun-time
[2025-05-23T23:53:49.012Z] 跳过已处理的资源目录: family-barbecue-fun-time
[2025-05-23T23:53:49.012Z] 处理资源目录: little-gardeners-sunny-day
[2025-05-23T23:53:49.013Z] 跳过已完全处理的资源目录: little-gardeners-sunny-day
[2025-05-23T23:53:49.013Z] 跳过已处理的资源目录: little-gardeners-sunny-day
[2025-05-23T23:53:49.013Z] 处理资源目录: majestic-fairy-tale-castle
[2025-05-23T23:53:49.013Z] 跳过已完全处理的资源目录: majestic-fairy-tale-castle
[2025-05-23T23:53:49.013Z] 跳过已处理的资源目录: majestic-fairy-tale-castle
[2025-05-23T23:53:49.013Z] 处理资源目录: patriotic-backyard-celebration
[2025-05-23T23:53:49.013Z] 跳过已完全处理的资源目录: patriotic-backyard-celebration
[2025-05-23T23:53:49.013Z] 跳过已处理的资源目录: patriotic-backyard-celebration
[2025-05-23T23:53:49.013Z] 处理资源目录: route-sixty-six-adventure
[2025-05-23T23:53:49.013Z] 跳过已完全处理的资源目录: route-sixty-six-adventure
[2025-05-23T23:53:49.013Z] 跳过已处理的资源目录: route-sixty-six-adventure
[2025-05-23T23:53:49.014Z] 已保存 9 个已处理资源的记录
[2025-05-23T23:53:49.014Z] 处理资源目录: santorini-snapshot-serenity
[2025-05-23T23:53:49.014Z] 跳过已完全处理的资源目录: santorini-snapshot-serenity
[2025-05-23T23:53:49.014Z] 跳过已处理的资源目录: santorini-snapshot-serenity
[2025-05-23T23:53:49.014Z] 已保存 9 个已处理资源的记录
[2025-05-23T23:53:49.014Z] 资源同步完成
[2025-05-23T23:53:49.014Z] 总结: 总计 6 个资源目录, 成功 0, 跳过 6, 失败 0
[2025-05-24T00:00:09.535Z] 已加载 9 个已处理资源的记录
[2025-05-24T00:00:09.539Z] 开始同步资源文件...
[2025-05-24T00:00:09.539Z] 配置: {
  "resourceDir": "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages",
  "contentDir": "/Users/<USER>/printablecoloringhub/src/content/coloring-pages",
  "skipProcessed": true,
  "dryRun": false
}
[2025-05-24T00:00:09.540Z] 找到 22 个资源目录
[2025-05-24T00:00:09.540Z] 处理资源目录: bavarian-festival-fun
[2025-05-24T00:00:09.543Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/bavarian-festival-fun/bavarian-festival-fun.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/bavarian-festival-fun.mdx
[2025-05-24T00:00:09.543Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/bavarian-festival-fun/bavarian-festival-fun_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/bavarian-festival-fun/bavarian-festival-fun_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"bavarian-festival-fun_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:12.304Z] 成功上传文件: coloring-pages/bavarian-festival-fun/bavarian-festival-fun_colored.png
[2025-05-24T00:00:12.305Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/bavarian-festival-fun/bavarian-festival-fun_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/bavarian-festival-fun/bavarian-festival-fun_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"bavarian-festival-fun_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:14.740Z] 成功上传文件: coloring-pages/bavarian-festival-fun/bavarian-festival-fun_monochrome.pdf
[2025-05-24T00:00:14.741Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/bavarian-festival-fun/bavarian-festival-fun_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/bavarian-festival-fun/bavarian-festival-fun_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"bavarian-festival-fun_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:17.031Z] 成功上传文件: coloring-pages/bavarian-festival-fun/bavarian-festival-fun_monochrome.png
[2025-05-24T00:00:17.031Z] 成功处理资源目录: bavarian-festival-fun
[2025-05-24T00:00:17.031Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:00:17.031Z] 处理资源目录: beach-music-festival-scene
[2025-05-24T00:00:17.032Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/beach-music-festival-scene/beach-music-festival-scene.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/beach-music-festival-scene.mdx
[2025-05-24T00:00:17.032Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/beach-music-festival-scene/beach-music-festival-scene_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/beach-music-festival-scene/beach-music-festival-scene_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"beach-music-festival-scene_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:19.315Z] 成功上传文件: coloring-pages/beach-music-festival-scene/beach-music-festival-scene_colored.png
[2025-05-24T00:00:19.315Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/beach-music-festival-scene/beach-music-festival-scene_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/beach-music-festival-scene/beach-music-festival-scene_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"beach-music-festival-scene_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:21.326Z] 成功上传文件: coloring-pages/beach-music-festival-scene/beach-music-festival-scene_monochrome.pdf
[2025-05-24T00:00:21.326Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/beach-music-festival-scene/beach-music-festival-scene_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/beach-music-festival-scene/beach-music-festival-scene_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"beach-music-festival-scene_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:23.673Z] 成功上传文件: coloring-pages/beach-music-festival-scene/beach-music-festival-scene_monochrome.png
[2025-05-24T00:00:23.673Z] 成功处理资源目录: beach-music-festival-scene
[2025-05-24T00:00:23.673Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:00:23.674Z] 处理资源目录: cozy-family-reading-time
[2025-05-24T00:00:23.675Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/cozy-family-reading-time/cozy-family-reading-time.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/cozy-family-reading-time.mdx
[2025-05-24T00:00:23.675Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/cozy-family-reading-time/cozy-family-reading-time_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/cozy-family-reading-time/cozy-family-reading-time_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"cozy-family-reading-time_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:26.623Z] 成功上传文件: coloring-pages/cozy-family-reading-time/cozy-family-reading-time_colored.png
[2025-05-24T00:00:26.623Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/cozy-family-reading-time/cozy-family-reading-time_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/cozy-family-reading-time/cozy-family-reading-time_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"cozy-family-reading-time_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:28.857Z] 成功上传文件: coloring-pages/cozy-family-reading-time/cozy-family-reading-time_monochrome.pdf
[2025-05-24T00:00:28.857Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/cozy-family-reading-time/cozy-family-reading-time_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/cozy-family-reading-time/cozy-family-reading-time_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"cozy-family-reading-time_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:31.485Z] 成功上传文件: coloring-pages/cozy-family-reading-time/cozy-family-reading-time_monochrome.png
[2025-05-24T00:00:31.485Z] 成功处理资源目录: cozy-family-reading-time
[2025-05-24T00:00:31.486Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:00:31.486Z] 处理资源目录: european-market-delight
[2025-05-24T00:00:31.487Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/european-market-delight/european-market-delight.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/european-market-delight.mdx
[2025-05-24T00:00:31.487Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/european-market-delight/european-market-delight_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/european-market-delight/european-market-delight_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"european-market-delight_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:33.825Z] 成功上传文件: coloring-pages/european-market-delight/european-market-delight_colored.png
[2025-05-24T00:00:33.826Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/european-market-delight/european-market-delight_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/european-market-delight/european-market-delight_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"european-market-delight_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:36.074Z] 成功上传文件: coloring-pages/european-market-delight/european-market-delight_monochrome.pdf
[2025-05-24T00:00:36.074Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/european-market-delight/european-market-delight_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/european-market-delight/european-market-delight_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"european-market-delight_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:38.486Z] 成功上传文件: coloring-pages/european-market-delight/european-market-delight_monochrome.png
[2025-05-24T00:00:38.486Z] 成功处理资源目录: european-market-delight
[2025-05-24T00:00:38.487Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:00:38.487Z] 处理资源目录: family-barbecue-fun-time
[2025-05-24T00:00:38.487Z] 跳过已完全处理的资源目录: family-barbecue-fun-time
[2025-05-24T00:00:38.487Z] 跳过已处理的资源目录: family-barbecue-fun-time
[2025-05-24T00:00:38.487Z] 已保存 13 个已处理资源的记录
[2025-05-24T00:00:38.487Z] 处理资源目录: family-beach-volleyball-fun
[2025-05-24T00:00:38.488Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/family-beach-volleyball-fun.mdx
[2025-05-24T00:00:38.488Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"family-beach-volleyball-fun_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:40.756Z] 成功上传文件: coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_colored.png
[2025-05-24T00:00:40.757Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"family-beach-volleyball-fun_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:42.778Z] 成功上传文件: coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_monochrome.pdf
[2025-05-24T00:00:42.778Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"family-beach-volleyball-fun_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:45.338Z] 成功上传文件: coloring-pages/family-beach-volleyball-fun/family-beach-volleyball-fun_monochrome.png
[2025-05-24T00:00:45.338Z] 成功处理资源目录: family-beach-volleyball-fun
[2025-05-24T00:00:45.338Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:00:45.338Z] 处理资源目录: little-gardeners-sunny-day
[2025-05-24T00:00:45.339Z] 跳过已完全处理的资源目录: little-gardeners-sunny-day
[2025-05-24T00:00:45.339Z] 跳过已处理的资源目录: little-gardeners-sunny-day
[2025-05-24T00:00:45.339Z] 处理资源目录: london-food-festival-fun
[2025-05-24T00:00:45.340Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/london-food-festival-fun/london-food-festival-fun.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/london-food-festival-fun.mdx
[2025-05-24T00:00:45.340Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/london-food-festival-fun/london-food-festival-fun_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/london-food-festival-fun/london-food-festival-fun_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"london-food-festival-fun_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:48.182Z] 成功上传文件: coloring-pages/london-food-festival-fun/london-food-festival-fun_colored.png
[2025-05-24T00:00:48.183Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/london-food-festival-fun/london-food-festival-fun_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/london-food-festival-fun/london-food-festival-fun_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"london-food-festival-fun_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:50.304Z] 成功上传文件: coloring-pages/london-food-festival-fun/london-food-festival-fun_monochrome.pdf
[2025-05-24T00:00:50.304Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/london-food-festival-fun/london-food-festival-fun_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/london-food-festival-fun/london-food-festival-fun_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"london-food-festival-fun_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:52.936Z] 成功上传文件: coloring-pages/london-food-festival-fun/london-food-festival-fun_monochrome.png
[2025-05-24T00:00:52.936Z] 成功处理资源目录: london-food-festival-fun
[2025-05-24T00:00:52.936Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:00:52.936Z] 处理资源目录: majestic-fairy-tale-castle
[2025-05-24T00:00:52.937Z] 跳过已完全处理的资源目录: majestic-fairy-tale-castle
[2025-05-24T00:00:52.937Z] 跳过已处理的资源目录: majestic-fairy-tale-castle
[2025-05-24T00:00:52.937Z] 处理资源目录: major-league-baseball-game
[2025-05-24T00:00:52.938Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/major-league-baseball-game/major-league-baseball-game.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/major-league-baseball-game.mdx
[2025-05-24T00:00:52.938Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/major-league-baseball-game/major-league-baseball-game_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/major-league-baseball-game/major-league-baseball-game_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"major-league-baseball-game_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:55.504Z] 成功上传文件: coloring-pages/major-league-baseball-game/major-league-baseball-game_colored.png
[2025-05-24T00:00:55.505Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/major-league-baseball-game/major-league-baseball-game_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/major-league-baseball-game/major-league-baseball-game_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"major-league-baseball-game_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:00:57.804Z] 成功上传文件: coloring-pages/major-league-baseball-game/major-league-baseball-game_monochrome.pdf
[2025-05-24T00:00:57.804Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/major-league-baseball-game/major-league-baseball-game_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/major-league-baseball-game/major-league-baseball-game_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"major-league-baseball-game_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:00.298Z] 成功上传文件: coloring-pages/major-league-baseball-game/major-league-baseball-game_monochrome.png
[2025-05-24T00:01:00.298Z] 成功处理资源目录: major-league-baseball-game
[2025-05-24T00:01:00.298Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:01:00.299Z] 已保存 16 个已处理资源的记录
[2025-05-24T00:01:00.299Z] 处理资源目录: nice-carnival-parade-celebration
[2025-05-24T00:01:00.300Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/nice-carnival-parade-celebration.mdx
[2025-05-24T00:01:00.300Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"nice-carnival-parade-celebration_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:02.820Z] 成功上传文件: coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_colored.png
[2025-05-24T00:01:02.820Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"nice-carnival-parade-celebration_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:05.074Z] 成功上传文件: coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_monochrome.pdf
[2025-05-24T00:01:05.074Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"nice-carnival-parade-celebration_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:07.827Z] 成功上传文件: coloring-pages/nice-carnival-parade-celebration/nice-carnival-parade-celebration_monochrome.png
[2025-05-24T00:01:07.828Z] 成功处理资源目录: nice-carnival-parade-celebration
[2025-05-24T00:01:07.828Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:01:07.828Z] 处理资源目录: notting-hill-carnival-celebration
[2025-05-24T00:01:07.829Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/notting-hill-carnival-celebration.mdx
[2025-05-24T00:01:07.829Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"notting-hill-carnival-celebration_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:10.372Z] 成功上传文件: coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_colored.png
[2025-05-24T00:01:10.372Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"notting-hill-carnival-celebration_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:12.705Z] 成功上传文件: coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_monochrome.pdf
[2025-05-24T00:01:12.705Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"notting-hill-carnival-celebration_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:15.431Z] 成功上传文件: coloring-pages/notting-hill-carnival-celebration/notting-hill-carnival-celebration_monochrome.png
[2025-05-24T00:01:15.431Z] 成功处理资源目录: notting-hill-carnival-celebration
[2025-05-24T00:01:15.431Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:01:15.431Z] 处理资源目录: patriotic-backyard-celebration
[2025-05-24T00:01:15.431Z] 跳过已完全处理的资源目录: patriotic-backyard-celebration
[2025-05-24T00:01:15.432Z] 跳过已处理的资源目录: patriotic-backyard-celebration
[2025-05-24T00:01:15.432Z] 处理资源目录: patriotic-sky-explosions
[2025-05-24T00:01:15.432Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/patriotic-sky-explosions.mdx
[2025-05-24T00:01:15.432Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"patriotic-sky-explosions_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:17.964Z] 成功上传文件: coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_colored.png
[2025-05-24T00:01:17.964Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"patriotic-sky-explosions_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:20.312Z] 成功上传文件: coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_monochrome.pdf
[2025-05-24T00:01:20.312Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"patriotic-sky-explosions_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:23.056Z] 成功上传文件: coloring-pages/patriotic-sky-explosions/patriotic-sky-explosions_monochrome.png
[2025-05-24T00:01:23.056Z] 成功处理资源目录: patriotic-sky-explosions
[2025-05-24T00:01:23.056Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:01:23.056Z] 处理资源目录: route-sixty-six-adventure
[2025-05-24T00:01:23.056Z] 跳过已完全处理的资源目录: route-sixty-six-adventure
[2025-05-24T00:01:23.056Z] 跳过已处理的资源目录: route-sixty-six-adventure
[2025-05-24T00:01:23.057Z] 已保存 19 个已处理资源的记录
[2025-05-24T00:01:23.057Z] 处理资源目录: santorini-snapshot-serenity
[2025-05-24T00:01:23.057Z] 跳过已完全处理的资源目录: santorini-snapshot-serenity
[2025-05-24T00:01:23.057Z] 跳过已处理的资源目录: santorini-snapshot-serenity
[2025-05-24T00:01:23.057Z] 处理资源目录: scenic-mountain-road-trip
[2025-05-24T00:01:23.058Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/scenic-mountain-road-trip.mdx
[2025-05-24T00:01:23.059Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"scenic-mountain-road-trip_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:25.455Z] 成功上传文件: coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_colored.png
[2025-05-24T00:01:25.455Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"scenic-mountain-road-trip_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:27.843Z] 成功上传文件: coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_monochrome.pdf
[2025-05-24T00:01:27.843Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"scenic-mountain-road-trip_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:30.455Z] 成功上传文件: coloring-pages/scenic-mountain-road-trip/scenic-mountain-road-trip_monochrome.png
[2025-05-24T00:01:30.455Z] 成功处理资源目录: scenic-mountain-road-trip
[2025-05-24T00:01:30.455Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:01:30.455Z] 处理资源目录: sunny-beach-getaway
[2025-05-24T00:01:30.456Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/sunny-beach-getaway/sunny-beach-getaway.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/sunny-beach-getaway.mdx
[2025-05-24T00:01:30.456Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/sunny-beach-getaway/sunny-beach-getaway_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/sunny-beach-getaway/sunny-beach-getaway_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"sunny-beach-getaway_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:33.154Z] 成功上传文件: coloring-pages/sunny-beach-getaway/sunny-beach-getaway_colored.png
[2025-05-24T00:01:33.154Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/sunny-beach-getaway/sunny-beach-getaway_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/sunny-beach-getaway/sunny-beach-getaway_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"sunny-beach-getaway_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:35.327Z] 成功上传文件: coloring-pages/sunny-beach-getaway/sunny-beach-getaway_monochrome.pdf
[2025-05-24T00:01:35.327Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/sunny-beach-getaway/sunny-beach-getaway_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/sunny-beach-getaway/sunny-beach-getaway_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"sunny-beach-getaway_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:37.817Z] 成功上传文件: coloring-pages/sunny-beach-getaway/sunny-beach-getaway_monochrome.png
[2025-05-24T00:01:37.817Z] 成功处理资源目录: sunny-beach-getaway
[2025-05-24T00:01:37.817Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:01:37.817Z] 处理资源目录: sunny-surf-day-adventure
[2025-05-24T00:01:37.818Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/sunny-surf-day-adventure.mdx
[2025-05-24T00:01:37.818Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"sunny-surf-day-adventure_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:40.746Z] 成功上传文件: coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_colored.png
[2025-05-24T00:01:40.746Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"sunny-surf-day-adventure_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:42.994Z] 成功上传文件: coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_monochrome.pdf
[2025-05-24T00:01:42.995Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"sunny-surf-day-adventure_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:45.685Z] 成功上传文件: coloring-pages/sunny-surf-day-adventure/sunny-surf-day-adventure_monochrome.png
[2025-05-24T00:01:45.686Z] 成功处理资源目录: sunny-surf-day-adventure
[2025-05-24T00:01:45.686Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:01:45.686Z] 处理资源目录: thanksgiving-parade-spectacle
[2025-05-24T00:01:45.687Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/thanksgiving-parade-spectacle.mdx
[2025-05-24T00:01:45.687Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"thanksgiving-parade-spectacle_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:48.819Z] 成功上传文件: coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_colored.png
[2025-05-24T00:01:48.820Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"thanksgiving-parade-spectacle_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:50.975Z] 成功上传文件: coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_monochrome.pdf
[2025-05-24T00:01:50.975Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"thanksgiving-parade-spectacle_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:53.338Z] 成功上传文件: coloring-pages/thanksgiving-parade-spectacle/thanksgiving-parade-spectacle_monochrome.png
[2025-05-24T00:01:53.338Z] 成功处理资源目录: thanksgiving-parade-spectacle
[2025-05-24T00:01:53.339Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:01:53.339Z] 已保存 23 个已处理资源的记录
[2025-05-24T00:01:53.339Z] 处理资源目录: tour-de-france-triumph
[2025-05-24T00:01:53.340Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/tour-de-france-triumph/tour-de-france-triumph.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/tour-de-france-triumph.mdx
[2025-05-24T00:01:53.340Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/tour-de-france-triumph/tour-de-france-triumph_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/tour-de-france-triumph/tour-de-france-triumph_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"tour-de-france-triumph_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:56.070Z] 成功上传文件: coloring-pages/tour-de-france-triumph/tour-de-france-triumph_colored.png
[2025-05-24T00:01:56.070Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/tour-de-france-triumph/tour-de-france-triumph_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/tour-de-france-triumph/tour-de-france-triumph_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"tour-de-france-triumph_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:01:58.408Z] 成功上传文件: coloring-pages/tour-de-france-triumph/tour-de-france-triumph_monochrome.pdf
[2025-05-24T00:01:58.409Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/tour-de-france-triumph/tour-de-france-triumph_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/tour-de-france-triumph/tour-de-france-triumph_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"tour-de-france-triumph_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:02:01.120Z] 成功上传文件: coloring-pages/tour-de-france-triumph/tour-de-france-triumph_monochrome.png
[2025-05-24T00:02:01.120Z] 成功处理资源目录: tour-de-france-triumph
[2025-05-24T00:02:01.120Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:02:01.120Z] 处理资源目录: yellowstone-camping-scene
[2025-05-24T00:02:01.121Z] 成功移动 MDX 文件: /Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene.mdx -> /Users/<USER>/printablecoloringhub/src/content/coloring-pages/yellowstone-camping-scene.mdx
[2025-05-24T00:02:01.121Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_colored.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_colored.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"yellowstone-camping-scene_colored.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:02:03.789Z] 成功上传文件: coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_colored.png
[2025-05-24T00:02:03.790Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_monochrome.pdf       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"yellowstone-camping-scene_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:02:06.153Z] 成功上传文件: coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_monochrome.pdf
[2025-05-24T00:02:06.153Z] 执行命令: aws s3api put-object       --bucket uslocal       --key coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_monochrome.png       --body "/Users/<USER>/printablecoloringhub/scripts/resource/coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_monochrome.png"       --content-type "image/png"       --content-disposition "attachment; filename=\"yellowstone-camping-scene_monochrome.png\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T00:02:08.777Z] 成功上传文件: coloring-pages/yellowstone-camping-scene/yellowstone-camping-scene_monochrome.png
[2025-05-24T00:02:08.777Z] 成功处理资源目录: yellowstone-camping-scene
[2025-05-24T00:02:08.777Z] 上传结果: 总计 3, 成功 3, 跳过 0, 失败 0
[2025-05-24T00:02:08.777Z] 已保存 25 个已处理资源的记录
[2025-05-24T00:02:08.777Z] 资源同步完成
[2025-05-24T00:02:08.777Z] 总结: 总计 22 个资源目录, 成功 16, 跳过 6, 失败 0
