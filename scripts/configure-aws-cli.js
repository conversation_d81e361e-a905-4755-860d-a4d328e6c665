/**
 * 配置 AWS CLI 使用 Cloudflare R2 凭证
 *
 * 该脚本从 .env 文件读取 Cloudflare R2 凭证，
 * 并配置 AWS CLI 使用这些凭证。
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件的目录路径（ES模块中的__dirname替代方案）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从 .env 文件加载环境变量
dotenv.config();

// 获取 Cloudflare R2 凭证
const accessKeyId = process.env.CLOUDFLARE_R2_ACCESS_KEY_ID;
const secretAccessKey = process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY;
const accountId = process.env.CLOUDFLARE_R2_ACCOUNT_ID;

if (!accessKeyId || !secretAccessKey || !accountId) {
  console.error('错误: 缺少 Cloudflare R2 凭证。请检查 .env 文件。');
  process.exit(1);
}

console.log('配置 AWS CLI 使用 Cloudflare R2 凭证...');

// 创建 AWS 配置目录
const awsDir = path.join(os.homedir(), '.aws');
if (!fs.existsSync(awsDir)) {
  fs.mkdirSync(awsDir, { recursive: true });
}

// 创建或更新 AWS 凭证文件
const credentialsPath = path.join(awsDir, 'credentials');
const credentialsContent = `[default]
aws_access_key_id = ${accessKeyId}
aws_secret_access_key = ${secretAccessKey}
`;

fs.writeFileSync(credentialsPath, credentialsContent);

// 创建或更新 AWS 配置文件
const configPath = path.join(awsDir, 'config');
const configContent = `[default]
region = auto
output = json
endpoint_url = https://${accountId}.r2.cloudflarestorage.com
`;

fs.writeFileSync(configPath, configContent);

console.log('AWS CLI 配置完成！');
console.log(`凭证文件: ${credentialsPath}`);
console.log(`配置文件: ${configPath}`);
console.log('');
console.log('现在您可以运行以下脚本:');
console.log('1. 更新现有文件的元数据: node scripts/update-r2-metadata.js');
console.log('2. 上传单个文件并设置元数据: node scripts/upload-to-r2-with-metadata.js <本地文件路径> <R2目标路径>');
console.log('3. 批量上传文件并设置元数据: node scripts/batch-upload-to-r2.js <本地文件夹路径> <R2目标前缀>');
