/**
 * Cloudflare R2 上传脚本
 *
 * 该脚本用于将文件上传到 Cloudflare R2 存储桶，
 * 并设置正确的 Content-Type 和 Content-Disposition: attachment 头。
 *
 * 使用方法:
 * 1. 确保已安装 AWS CLI 并配置了 Cloudflare R2 凭证
 * 2. 运行: node scripts/upload-to-r2-with-metadata.js <本地文件路径> <R2目标路径>
 *
 * 示例:
 * node scripts/upload-to-r2-with-metadata.js ./public/files/sample.pdf coloring-pages/sample.pdf
 *
 * 注意: 该脚本使用 AWS CLI 与 Cloudflare R2 交互，因为 R2 兼容 S3 API
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件的目录路径（ES模块中的__dirname替代方案）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从 .env 文件加载环境变量
dotenv.config();

// 配置
const CONFIG = {
  // R2 存储桶名称
  bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'uslocal',

  // R2 S3 API 端点 URL
  endpointUrl: `https://${process.env.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,

  // 是否仅模拟执行 (设为 true 进行测试，不会实际上传文件；设为 false 将实际执行上传)
  dryRun: false,

  // 日志文件路径
  logFile: path.join(__dirname, 'r2-upload.log')
};

// 初始化日志
const log = (message) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(CONFIG.logFile, logMessage + '\n');
};

// 清除旧日志
if (fs.existsSync(CONFIG.logFile)) {
  fs.unlinkSync(CONFIG.logFile);
}

// 检查命令行参数
if (process.argv.length < 4) {
  log('错误: 缺少必要的参数');
  log('用法: node upload-to-r2-with-metadata.js <本地文件路径> <R2目标路径>');
  process.exit(1);
}

const localFilePath = process.argv[2];
const r2DestinationPath = process.argv[3];

// 检查本地文件是否存在
if (!fs.existsSync(localFilePath)) {
  log(`错误: 本地文件不存在: ${localFilePath}`);
  process.exit(1);
}

log('开始上传文件到 Cloudflare R2...');
log(`本地文件: ${localFilePath}`);
log(`R2 目标路径: ${r2DestinationPath}`);

// 上传文件并设置元数据
const uploadFile = () => {
  try {
    // 获取文件扩展名
    const ext = path.extname(localFilePath).toLowerCase().substring(1);

    // 设置适当的 Content-Type
    let contentType = 'application/octet-stream';
    if (ext === 'pdf') {
      contentType = 'application/pdf';
    } else if (ext === 'png') {
      contentType = 'image/png';
    } else if (ext === 'jpg' || ext === 'jpeg') {
      contentType = 'image/jpeg';
    } else if (ext === 'webp') {
      contentType = 'image/webp';
    } else if (ext === 'svg') {
      contentType = 'image/svg+xml';
    }

    // 从路径中提取文件名
    const fileName = path.basename(localFilePath);

    // 构建 put-object 命令
    const command = `aws s3api put-object \
      --bucket ${CONFIG.bucketName} \
      --key ${r2DestinationPath} \
      --body ${localFilePath} \
      --content-type "${contentType}" \
      --content-disposition "attachment; filename=\\"${fileName}\\"" \
      --endpoint-url ${CONFIG.endpointUrl}`;

    log(`执行命令: ${command}`);

    if (CONFIG.dryRun) {
      log(`[DRY RUN] 跳过执行 put-object 命令`);
      return true;
    }

    execSync(command, { encoding: 'utf-8' });
    log(`成功上传文件: ${r2DestinationPath}`);
    return true;
  } catch (error) {
    log(`上传文件时出错: ${error.message}`);
    return false;
  }
};

// 执行上传
const success = uploadFile();
if (success) {
  log('文件上传成功');
} else {
  log('文件上传失败');
  process.exit(1);
}
