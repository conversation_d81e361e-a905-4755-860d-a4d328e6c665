---
interface Props {
  className?: string;
}

const { className = "" } = Astro.props;
---

<a
  href="/"
  class={`btn btn-primary inline-flex items-center px-4 py-2 rounded-md text-white transition-all hover:bg-primary-hover ${className}`}
>
  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
  </svg>
  <span>Back to Home</span>
</a>

<style>
  /* 基础样式 */
  a {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 500;
    transition: all 0.2s ease;
  }

  a:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  }

  a:active {
    transform: translateY(0);
  }

  /* 移动端优化样式 */
  @media (max-width: 640px) {
    a {
      font-size: 0.875rem;
      padding: 0.5rem 0.75rem;
      width: auto;
      display: inline-flex;
      justify-content: center;
    }

    svg {
      width: 1rem;
      height: 1rem;
    }
  }
</style>
