---
interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

interface Props {
  items: BreadcrumbItem[];
  className?: string;
}

const { items, className = "" } = Astro.props;
---

<nav aria-label="Breadcrumb" class={`mb-6 ${className}`}>
  <ol class="flex flex-wrap items-center">
    {items.map((item, index) => (
      <li class="flex items-center">
        {index > 0 && (
          <span class="mx-2 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </span>
        )}
        
        {item.isActive ? (
          <span class="text-primary font-medium" aria-current="page">{item.label}</span>
        ) : item.href ? (
          <a 
            href={item.href} 
            class="text-gray-600 hover:text-primary transition-colors duration-200 font-medium"
          >
            {item.label}
          </a>
        ) : (
          <span class="text-gray-600">{item.label}</span>
        )}
      </li>
    ))}
  </ol>
</nav>

<style>
  /* 美化面包屑样式 */
  nav {
    background-color: white;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  ol {
    display: flex;
    flex-wrap: wrap;
  }

  li {
    display: flex;
    align-items: center;
  }

  a, span {
    font-size: 0.95rem;
  }

  a:hover {
    text-decoration: underline;
  }

  /* 响应式调整 */
  @media (max-width: 640px) {
    nav {
      padding: 0.5rem 0.75rem;
    }
    
    a, span {
      font-size: 0.85rem;
    }
  }
</style>
