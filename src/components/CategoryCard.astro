---
import { Image } from 'astro:assets';
import { getCategoryImage } from '@/data/categoryImages';

interface Props {
  category: {
    name: string;
    slug: string;
    count: number;
    subcategories: Array<{
      name: string;
      slug: string;
      count: number;
    }>;
  };
  searchTerm?: string; // 可选的搜索词，用于高亮显示匹配的子分类
}

const { category, searchTerm = '' } = Astro.props;
const { name, slug, count, subcategories } = category;

// 获取分类图片
const categoryImage = getCategoryImage(name);

// 最多显示6个子分类
const displaySubcategories = subcategories.slice(0, 6);
const hasMoreSubcategories = subcategories.length > 6;

// 检查是否有搜索词匹配子分类
const hasMatchingSubcategory = searchTerm && subcategories.some(
  sub => sub.name.toLowerCase().includes(searchTerm.toLowerCase())
);
---

<div class="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md hover:-translate-y-1">
  <div class="aspect-[4/3] overflow-hidden relative">
    <a href={`/${slug}`} class="block w-full h-full">
      <Image
        src={categoryImage}
        alt={`${name} coloring pages`}
        width={400}
        height={300}
        class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
        format="webp"
        quality={80}
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end">
        <div class="p-4 text-white">
          <h2 class="text-xl font-semibold capitalize mb-1">{name}</h2>
          <p class="text-sm opacity-90">{count} coloring pages</p>
        </div>
      </div>
    </a>
  </div>

  {subcategories.length > 0 && (
    <div class={`p-4 ${hasMatchingSubcategory ? 'bg-yellow-50' : ''}`}>
      {hasMatchingSubcategory && (
        <div class="mb-2 text-xs text-primary font-medium">
          Found matching subcategories
        </div>
      )}
      <ul class="grid grid-cols-2 gap-2">
        {displaySubcategories.map((sub: {name: string, slug: string, count: number}) => {
          // 检查子分类是否匹配搜索词
          const isMatch = searchTerm && sub.name.toLowerCase().includes(searchTerm.toLowerCase());

          return (
            <li>
              <a
                href={`/${sub.slug}`}
                class={`text-sm hover:text-primary flex items-center gap-1 transition-colors ${
                  isMatch
                    ? 'font-medium text-primary bg-primary/10 px-2 py-1 rounded-full'
                    : 'text-gray-700'
                }`}
              >
                <span class="capitalize">{sub.name}</span>
                <span class={`text-xs ${isMatch ? 'text-primary/70' : 'text-gray-500'}`}>({sub.count})</span>
              </a>
            </li>
          );
        })}
      </ul>

      {hasMoreSubcategories && (
        <div class="mt-3 text-center">
          <a
            href={`/${slug}`}
            class="text-xs text-primary hover:text-primary-hover inline-flex items-center"
          >
            <span>View all {subcategories.length} subcategories</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </a>
        </div>
      )}
    </div>
  )}
</div>
