---
import { getR2ImageUrl } from '@/utils/cloudflareUtils';
import { CLOUDFLARE_IMAGES } from '@/config/cloudflare';
import CloudflareOptimizedImage from './CloudflareOptimizedImage.astro';

interface Props {
  id: string;
  slug?: string;
  title: string;
  assetFolder: string;  // 资源文件夹名称
  categoryInfo?: {
    main: string;
    sub?: string;
    subsub?: string;
  };
  tags?: string[];     // 标签数组
  isFree?: boolean;   // 是否免费
}

const {
  id,
  slug,
  title,
  assetFolder,
  categoryInfo,
  tags = [],
  isFree = true,
} = Astro.props;

const pageUrl = slug ? `/coloring-pages/${slug}` : `/coloring-pages/${id}`;

// 获取卡片预设配置
const cardPreset = CLOUDFLARE_IMAGES.presets.card;

// 图片URL（用于下载）（可能是逗号分隔的多个URL）
const pdfDownloadUrlsString = getR2ImageUrl(assetFolder, 'monochrome.pdf');
const pngDownloadUrlsString = getR2ImageUrl(assetFolder, 'monochrome.png');
// 分割URL字符串为数组
const pdfDownloadUrls = pdfDownloadUrlsString.split(',');
const pngDownloadUrls = pngDownloadUrlsString.split(',');
// 使用第一个URL作为默认URL
const pdfDownloadUrl = pdfDownloadUrls[0];
const pngDownloadUrl = pngDownloadUrls[0];

// 标签显示逻辑
const displayTags = tags.slice(0, 4); // 最多显示4个标签

// 为数据属性准备分类和标签信息
const dataCategory = categoryInfo?.main?.toLowerCase().replace(/\s+/g, '-') || '';
const dataSubcategory = categoryInfo?.sub?.toLowerCase().replace(/\s+/g, '-') || '';
const dataTags = tags.join(',').toLowerCase();
---



<div class="flex flex-col bg-white rounded-lg overflow-hidden transition-all duration-200 h-full relative border border-gray-200 shadow-sm hover:translate-y-[-2px] hover:shadow-md coloring-card" data-category={dataCategory} data-subcategory={dataSubcategory} data-tags={dataTags}>
  <figure class="m-0 relative w-full">
    <a href={pageUrl} class="no-underline text-inherit block relative z-[5]">
      <div class="aspect-square overflow-hidden relative rounded group">
        {/* 黑白图片（默认显示） */}
        <div class="w-full h-full transition-opacity duration-300 ease-in-out group-hover:opacity-0">
          <CloudflareOptimizedImage
            assetFolder={assetFolder}
            filename="monochrome.png"
            alt={`${title} coloring page`}
            width={500}
            height={500}
            loading="eager"
            class="w-full h-full object-cover"
            preset="card"
            format="auto"
            fit="cover"
            quality={80}
            showPlaceholder={true}
            dpr={2}
          />
        </div>

        {/* 彩色图片（悬停时显示） */}
        <div class="absolute inset-0 opacity-0 transition-opacity duration-300 ease-in-out delay-100 z-[8] group-hover:opacity-100">
          <CloudflareOptimizedImage
            assetFolder={assetFolder}
            filename="colored.png"
            alt={`${title} colored version`}
            width={500}
            height={500}
            loading="lazy"
            class="w-full h-full object-cover"
            preset="card"
            format="auto"
            fit="cover"
            quality={80}
            showPlaceholder={false}
            dpr={2}
          />
        </div>

        {/* 悬停遮罩 */}
        <div class="absolute inset-0 bg-black/5 opacity-0 transition-opacity duration-300 ease-in-out pointer-events-none z-[10] group-hover:opacity-100">
          {/* 底部区域 - 查看详情提示 */}
          <div class="absolute bottom-3 left-0 right-0 flex justify-center items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out delay-200">
            <div class="text-[11px] font-medium text-white flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              View Detail
            </div>
          </div>
        </div>

        {/* 免费/付费标签 */}
        <div class="absolute top-3 left-3 py-0.5 px-2 rounded text-[11px] font-medium border z-[15] opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out delay-150 pointer-events-none flex items-center bg-white text-emerald-600 border-emerald-200 before:content-[''] before:inline-block before:w-1.5 before:h-1.5 before:bg-emerald-500 before:rounded-full before:mr-1">
          {isFree ? 'Free' : 'Premium'}
        </div>

        {/* 彩色参考按钮 */}
        <div class="absolute top-3 right-3 py-0.5 px-2 rounded text-[11px] font-medium border border-green-200 bg-white text-green-600 z-[15] opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out delay-150 pointer-events-none flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-green-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd"></path>
          </svg>
          Color Reference
        </div>
      </div>
    </a>
    <a href={pageUrl} class="no-underline text-inherit">
      <figcaption class="text-sm font-normal my-2 mx-0 px-2 text-gray-700 overflow-hidden line-clamp-1 leading-tight whitespace-nowrap text-ellipsis" title={title}>{title}</figcaption>
    </a>
  </figure>

  <div class="flex flex-col px-2 pb-2 min-h-[40px]">
    {/* 标签 */}
    <div class="flex flex-wrap gap-1 mt-1">
      {displayTags.map((tag) => (
        <a
          href={`/tags/${tag.toLowerCase().replace(/\s+/g, '-')}`}
          class="text-[10px] text-gray-600 no-underline py-1 px-2 rounded-full bg-gray-100 transition-all duration-200 hover:bg-purple-100 hover:text-purple-700 hover:shadow-sm whitespace-nowrap border border-transparent hover:border-purple-200"
        >
          #{tag}
        </a>
      ))}
    </div>

    {/* 下载按钮 */}
    <div class="flex flex-col gap-1.5 mt-3">
      <div class="flex gap-1.5 w-full">
        {/* PNG按钮 */}
        {pngDownloadUrl && (
          <a
            href={pngDownloadUrl}
            download={`${title.toLowerCase().replace(/\s+/g, '-')}.png`}
            title="Download PNG"
            class="flex-1 flex items-center justify-center py-1 px-2 text-[11px] font-medium rounded border border-gray-200 text-emerald-600 bg-white hover:bg-emerald-50 hover:border-emerald-300 hover:text-emerald-700 transition-all duration-200 ease-in-out"
            aria-label="Download PNG"
            data-file-type="png"
            data-multiple-urls
            data-urls={pngDownloadUrlsString}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>PNG</span>
          </a>
        )}

        {/* PDF按钮 */}
        {pdfDownloadUrl && (
          <a
            href={pdfDownloadUrl}
            download={`${title.toLowerCase().replace(/\s+/g, '-')}.pdf`}
            title="Download PDF"
            class="flex-1 flex items-center justify-center py-1 px-2 text-[11px] font-medium rounded border border-gray-200 text-blue-600 bg-white hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200 ease-in-out"
            aria-label="Download PDF"
            data-file-type="pdf"
            data-multiple-urls
            data-urls={pdfDownloadUrlsString}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>PDF</span>
          </a>
        )}
      </div>
    </div>
  </div>
</div>
