---
import CloudflareOptimizedImage from './CloudflareOptimizedImage.astro';

interface Props {
  assetFolder: string;
  filename?: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'eager' | 'lazy';
  preset?: 'detail' | 'full';
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  quality?: number;
  format?: 'auto' | 'webp' | 'avif' | 'jpeg' | 'png';
  showPlaceholder?: boolean;
  dpr?: number;
  gravity?: 'auto' | 'center' | 'north' | 'south' | 'east' | 'west';
}

const {
  assetFolder,
  filename = 'monochrome.png',
  alt,
  width,
  height,
  class: className = 'w-full rounded-lg shadow-lg',
  loading = 'lazy',
  preset = 'detail',
  fit,
  quality,
  format = 'auto',
  showPlaceholder = true,
  dpr,
  gravity
} = Astro.props;
---

<CloudflareOptimizedImage
  assetFolder={assetFolder}
  filename={filename}
  alt={alt}
  width={width}
  height={height}
  class={className}
  loading={loading}
  preset={preset}
  fit={fit}
  quality={quality}
  format={format}
  showPlaceholder={showPlaceholder}
  dpr={dpr}
  gravity={gravity}
/>
