---
import { getR2ImageUrl } from '@/utils/cloudflareUtils';

interface Props {
  assetFolder: string;
  filename?: string;
  format: 'PDF' | 'PNG';
  className?: string;
  fileName?: string; // 添加文件名属性
}

const {
  assetFolder,
  filename,
  format,
  className = '',
  fileName = `coloring-page.${format.toLowerCase()}`
} = Astro.props;

// 根据格式确定默认文件名
const defaultFilename = format === 'PDF' ? 'monochrome.pdf' : 'monochrome.png';

// 使用提供的文件名或默认文件名
const fileToUse = filename || defaultFilename;

// 获取R2 URL（可能是逗号分隔的多个URL）
const fileUrlsString = getR2ImageUrl(assetFolder, fileToUse);
// 分割URL字符串为数组
const fileUrls = fileUrlsString.split(',');
// 使用第一个URL作为默认URL
const primaryFileUrl = fileUrls[0];

// 使用相同的样式类，不再根据格式区分
const buttonClasses = 'btn-primary';

// 如果没有安全的文件URL，则禁用按钮
const isDisabled = !primaryFileUrl;
---



{isDisabled ? (
  <button
    disabled
    class={`btn ${buttonClasses} ${className} opacity-50 cursor-not-allowed`}
  >
    <span class="mr-2">
      {format === 'PDF' ? (
        <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z" clip-rule="evenodd"/>
        </svg>
      ) : (
        <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-7Zm.394 9.553a1 1 0 0 0-1.817.062l-2.5 6A1 1 0 0 0 8 19h8a1 1 0 0 0 .894-1.447l-2-4A1 1 0 0 0 13.2 13.4l-.53.706-1.276-2.553ZM13 9.5a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z" clip-rule="evenodd"/>
        </svg>
      )}
    </span>
    Download {format}
  </button>
) : (
  format === 'PNG' ? (
    // 对PNG文件使用button元素，完全由JavaScript处理下载
    <button
      type="button"
      class={`btn ${buttonClasses} ${className} cursor-pointer`}
      data-file-type={format.toLowerCase()}
      data-multiple-urls
      data-urls={fileUrlsString}
      data-download-filename={fileName}
    >
      <span class="mr-2">
        <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-7Zm.394 9.553a1 1 0 0 0-1.817.062l-2.5 6A1 1 0 0 0 8 19h8a1 1 0 0 0 .894-1.447l-2-4A1 1 0 0 0 13.2 13.4l-.53.706-1.276-2.553ZM13 9.5a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z" clip-rule="evenodd"/>
        </svg>
      </span>
      Download {format}
    </button>
  ) : (
    // 对PDF文件使用a元素，浏览器可以正常处理
    <a
      href={primaryFileUrl}
      download={fileName}
      class={`btn ${buttonClasses} ${className}`}
      data-file-type={format.toLowerCase()}
      data-multiple-urls
      data-urls={fileUrlsString}
    >
      <span class="mr-2">
        <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z" clip-rule="evenodd"/>
        </svg>
      </span>
      Download {format}
    </a>
  )
)}
