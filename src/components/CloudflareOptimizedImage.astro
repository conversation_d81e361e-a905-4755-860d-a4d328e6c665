---
import { buildR2Url } from '@/config/cloudflare';
import { CLOUDFLARE_IMAGES } from '@/config/cloudflare';

interface Props {
  assetFolder: string;
  filename?: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'eager' | 'lazy';
  preset?: 'thumbnail' | 'card' | 'detail' | 'full';
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  quality?: number;
  format?: 'auto' | 'webp' | 'avif' | 'jpeg' | 'png';
  placeholderSize?: number; // 占位符尺寸，默认为20
  showPlaceholder?: boolean; // 是否显示占位符，默认为true
  dpr?: number; // 设备像素比
  gravity?: 'auto' | 'center' | 'north' | 'south' | 'east' | 'west'; // 裁剪焦点
}

const {
  assetFolder,
  filename = 'monochrome.png',
  alt,
  width,
  height,
  class: className = 'w-full rounded-lg shadow-lg',
  loading = 'lazy',
  preset = 'detail',
  fit,
  quality,
  format = 'auto',
  placeholderSize = 20,
  showPlaceholder = true,
  dpr,
  gravity
} = Astro.props;

// 获取预设配置
const presetConfig = CLOUDFLARE_IMAGES.presets[preset];

// 确保assetFolder不为空
if (!assetFolder) {
  console.error('Asset folder is missing');
}

// 构建主图片URL - 使用buildR2Url直接构建URL
// 使用第一个可能的文件名格式
const possibleFilenames = filename === 'monochrome.png'
  ? [`${assetFolder}_monochrome.png`, 'monochrome.png']
  : filename === 'monochrome.pdf'
  ? [`${assetFolder}_monochrome.pdf`, 'monochrome.pdf']
  : filename === 'colored.png'
  ? [`${assetFolder}_colored.png`, 'colored.png']
  : [filename];

// 构建所有可能的URL
const imageUrls = possibleFilenames.map(fname => {
  // 确保assetFolder和filename都不为空
  if (!assetFolder || !fname) return '';

  return buildR2Url(assetFolder, fname, {
    width: width || presetConfig.width,
    height: height || presetConfig.height,
    fit: (fit || presetConfig.fit) as 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad',
    quality: quality || presetConfig.quality,
    format: format,
    dpr: dpr,
    gravity: gravity
  });
}).filter(url => url && url.includes('/coloring-pages/')); // 确保URL包含资源路径

// 使用第一个URL，如果没有有效URL则使用空字符串
const imageUrl = imageUrls.length > 0 ? imageUrls[0] : '';

// 构建占位符URL（如果需要）
let placeholderUrl = '';
if (showPlaceholder) {
  // 使用相同的可能文件名列表
  const placeholderUrls = possibleFilenames.map(fname => {
    // 确保assetFolder和filename都不为空
    if (!assetFolder || !fname) return '';

    return buildR2Url(assetFolder, fname, {
      width: placeholderSize,
      height: placeholderSize,
      fit: 'cover' as const, // 使用类型断言
      quality: 60,
      format: 'webp'
    });
  }).filter(url => url && url.includes('/coloring-pages/')); // 确保URL包含资源路径

  // 使用第一个URL
  placeholderUrl = placeholderUrls.length > 0 ? placeholderUrls[0] : '';
}

// 图片加载状态类
const loadingClass = showPlaceholder ? 'opacity-0' : '';

// 构建srcset，用于响应式图像
const generateSrcSet = () => {
  if (!width) return '';

  // 使用设备像素比（DPR）而不是宽度变化
  const dprs = [1, 2, 3];

  // 只使用第一个可能的文件名来生成srcset
  if (possibleFilenames.length === 0 || !assetFolder) return '';

  const firstFilename = possibleFilenames[0];

  return dprs
    .map(dprValue => {
      const url = buildR2Url(assetFolder, firstFilename, {
        width: width,
        height: height,
        fit: (fit || presetConfig.fit) as 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad',
        quality: quality || presetConfig.quality,
        format: format,
        dpr: dprValue,
        gravity: gravity
      });
      if (!url || !url.includes('/coloring-pages/')) return '';
      return `${url} ${dprValue}x`;
    })
    .filter(Boolean)
    .join(', ');
};

const srcset = generateSrcSet();
---

<div class="relative overflow-hidden w-full h-full">
  {/* 占位符图片（模糊效果） */}
  {showPlaceholder && (
    <div class="absolute inset-0 w-full h-full">
      <img
        src={placeholderUrl}
        alt={`${alt} placeholder`}
        width={placeholderSize}
        height={placeholderSize}
        class="w-full h-full object-cover blur-sm scale-110 transition-opacity duration-300"
        loading="eager"
      />
    </div>
  )}

  {/* 主图片 */}
  <img
    src={imageUrl}
    srcset={srcset}
    alt={alt}
    width={width || presetConfig.width}
    height={height || presetConfig.height}
    loading={loading}
    class={`${className} ${loadingClass} transition-opacity duration-300 relative z-10`}
    onload="this.classList.remove('opacity-0'); if (this.previousElementSibling) this.previousElementSibling.classList.add('opacity-0');"
  />
</div>
