---
import { getCloudflareImagesUrl } from '@/utils/cloudflareUtils';
import { CLOUDFLARE_IMAGES } from '@/config/cloudflare';

interface Props {
  assetFolder: string;
  filename?: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'eager' | 'lazy';
  preset?: 'thumbnail' | 'card' | 'detail' | 'full';
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  quality?: number;
  format?: 'webp' | 'avif' | 'json' | 'jpeg' | 'png';
  placeholderSize?: number; // 占位符尺寸，默认为20
  showPlaceholder?: boolean; // 是否显示占位符，默认为true
}

const {
  assetFolder,
  filename = 'monochrome.png',
  alt,
  width,
  height,
  class: className = 'w-full rounded-lg shadow-lg',
  loading = 'lazy',
  preset = 'detail',
  fit,
  quality,
  format,
  placeholderSize = 20,
  showPlaceholder = true
} = Astro.props;

// 获取预设配置
const presetConfig = CLOUDFLARE_IMAGES.presets[preset];

// 构建主图片URL
const imageUrl = getCloudflareImagesUrl(assetFolder, filename, {
  width: width || presetConfig.width,
  height: height || presetConfig.height,
  fit: fit || presetConfig.fit as 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad',
  quality: quality || presetConfig.quality,
  format: format || presetConfig.format as 'webp' | 'avif' | 'json' | 'jpeg' | 'png',
  variant: preset
});

// 构建占位符URL（如果需要）
let placeholderUrl = '';
if (showPlaceholder) {
  placeholderUrl = getCloudflareImagesUrl(assetFolder, filename, {
    width: placeholderSize,
    height: placeholderSize,
    fit: 'cover',
    quality: 60,
    format: 'webp',
    variant: 'thumbnail'
  });
}

// 图片加载状态类
const loadingClass = showPlaceholder ? 'opacity-0' : '';
---

<div class="relative overflow-hidden w-full h-full">
  {/* 占位符图片（模糊效果） */}
  {showPlaceholder && (
    <div class="absolute inset-0 w-full h-full">
      <img
        src={placeholderUrl}
        alt={`${alt} placeholder`}
        width={placeholderSize}
        height={placeholderSize}
        class="w-full h-full object-cover blur-sm scale-110 transition-opacity duration-300"
        loading="eager"
      />
    </div>
  )}

  {/* 主图片 */}
  <img
    src={imageUrl}
    alt={alt}
    width={width || presetConfig.width}
    height={height || presetConfig.height}
    loading={loading}
    class={`${className} ${loadingClass} transition-opacity duration-300 relative z-10`}
    onload="this.classList.remove('opacity-0'); if (this.previousElementSibling) this.previousElementSibling.classList.add('opacity-0');"
    data-fallback-urls={imageUrl}
  />
</div>

<script>
// 图片加载失败处理
document.addEventListener('DOMContentLoaded', () => {
  const images = document.querySelectorAll('img[data-fallback-urls]');
  
  images.forEach(img => {
    img.addEventListener('error', function() {
      const fallbackUrls = this.getAttribute('data-fallback-urls')?.split(',') || [];
      const currentSrc = this.getAttribute('src');
      
      // 查找当前URL在备用URL列表中的索引
      const currentIndex = fallbackUrls.indexOf(currentSrc);
      
      // 如果有下一个备用URL，尝试加载它
      if (currentIndex >= 0 && currentIndex < fallbackUrls.length - 1) {
        const nextUrl = fallbackUrls[currentIndex + 1];
        this.setAttribute('src', nextUrl);
      }
    });
  });
});
</script>
