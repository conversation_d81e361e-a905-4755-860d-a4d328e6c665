---
import CloudflareColoringCard from '@/components/CloudflareColoringCard.astro';

interface Props {
  id: string;
  slug?: string;
  title: string;
  thumbnailImage?: any; // 保留以兼容现有代码
  assetFolder: string;  // 资源文件夹名称
  categoryInfo?: {
    main: string;
    sub?: string;
    subsub?: string;
  };
  tags?: string[];     // 标签数组
  isFree?: boolean;   // 是否免费
  pdfUrl?: string;    // PDF下载链接（保留以兼容现有代码）
  pngUrl?: string;    // PNG下载链接（保留以兼容现有代码）
}

const {
  id,
  slug,
  title,
  assetFolder,
  categoryInfo,
  tags = [],
  isFree = true,
} = Astro.props;

// 使用CloudflareColoringCard组件替代原有实现
---

<CloudflareColoringCard
  id={id}
  slug={slug}
  title={title}
  assetFolder={assetFolder}
  categoryInfo={categoryInfo}
  tags={tags}
  isFree={isFree}
/>

