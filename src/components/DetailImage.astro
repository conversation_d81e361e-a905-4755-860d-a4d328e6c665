---
import CloudflareDetailImage from '@/components/CloudflareDetailImage.astro';

interface Props {
  assetFolder: string;
  filename?: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'eager' | 'lazy';
}

const {
  assetFolder,
  filename = 'monochrome.png',
  alt,
  width = 800,
  height = 800,
  class: className = 'w-full rounded-lg shadow-lg',
  loading = 'lazy'
} = Astro.props;
---

<CloudflareDetailImage
  assetFolder={assetFolder}
  filename={filename}
  alt={alt}
  width={width}
  height={height}
  loading={loading}
  class={className}
/>

