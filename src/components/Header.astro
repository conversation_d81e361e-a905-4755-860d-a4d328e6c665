---
// 使用 Cloudflare R2 上的 logo
const logoUrl = "https://static.printablecoloringhub.com/logo/pch_logo.svg";
---

<header>
    <nav class="bg-white border-gray-200 px-4 lg:px-6 py-2.5 dark:bg-gray-800">
        <div class="flex flex-wrap items-center justify-between mx-auto w-11/12 max-w-screen-2xl relative mt-4">
            <!-- 左侧：Logo 部分 -->
            <div class="flex items-center">
                <a href="/" class="flex items-center">
                    <div class="mr-3 h-8 sm:h-10 w-auto logo-container flex items-center" aria-label="PrintableColoringHub Logo">
                        <img
                            src={logoUrl}
                            alt="PrintableColoringHub Logo"
                            width="40"
                            height="40"
                            class="logo-image"
                        />
                    </div>
                    <span class="self-center text-xl font-semibold whitespace-nowrap text-primary dark:text-white truncate">PrintableColoringHub</span>
                </a>
            </div>

            <!-- 中间：新内容提示 - 桌面版 -->
            <div class="hidden md:flex items-center flex-1 justify-center mx-4">
                <a href="/new" class="inline-flex items-center justify-between px-1 py-1 pr-4 text-sm text-gray-700 bg-gray-100 rounded-full dark:bg-gray-800 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700" role="alert">
                    <span class="badge badge-primary-solid px-3 py-1 mr-3 text-xs rounded-full">New</span> <span class="text-sm font-medium">Check out our latest coloring pages</span>
                    <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
                </a>
            </div>

            <!-- 右侧：导航菜单按钮 -->
            <div class="flex items-center">
                <button data-collapse-toggle="mobile-menu" type="button" class="inline-flex items-center p-2 text-sm text-gray-500 rounded-lg lg:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600" aria-controls="mobile-menu" aria-expanded="false">
                    <span class="sr-only">Open main menu</span>
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
                    <svg class="hidden w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                </button>
            </div>

            <!-- 导航菜单内容 -->
            <div class="hidden absolute top-full left-0 right-0 z-50 bg-white border-b border-gray-100 lg:static lg:border-0 lg:flex lg:w-auto lg:items-center lg:justify-end" id="mobile-menu">
                <!-- 移动端内容容器 -->
                <div class="px-4 py-2 lg:p-0">
                    <!-- 移动端新内容提示 -->
                    <div class="mt-4 mb-2 md:hidden">
                        <a href="/new" class="flex items-center justify-between px-1 py-1 pr-4 text-sm text-gray-700 bg-gray-100 rounded-full dark:bg-gray-800 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700" role="alert">
                            <span class="badge badge-primary-solid px-3 py-1 mr-3 text-xs rounded-full">New</span> <span class="text-sm font-medium">Check out our latest coloring pages</span>
                            <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
                        </a>
                    </div>
                    <!-- 导航菜单 -->
                    <ul class="flex flex-col mt-4 font-medium lg:flex-row lg:space-x-8 lg:mt-0">
                    <li>
                        <a href="/" class="block py-2 pr-4 pl-3 text-primary hover:text-primary-hover rounded lg:p-0" aria-current="page">Home</a>
                    </li>
                    <li>
                        <a href="/categories" class="block py-2 pr-4 pl-3 text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-white lg:border-0 lg:hover:text-primary lg:p-0 dark:text-gray-400 lg:dark:hover:text-white dark:hover:bg-gray-700 dark:hover:text-white lg:dark:hover:bg-transparent dark:border-gray-700">Categories</a>
                    </li>
                    <li>
                        <a href="/popular" class="block py-2 pr-4 pl-3 text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-white lg:border-0 lg:hover:text-primary lg:p-0 dark:text-gray-400 lg:dark:hover:text-white dark:hover:bg-gray-700 dark:hover:text-white lg:dark:hover:bg-transparent dark:border-gray-700">Popular</a>
                    </li>
                    <li>
                        <a href="/coloring-pages" class="block py-2 pr-4 pl-3 text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-white lg:border-0 lg:hover:text-primary lg:p-0 dark:text-gray-400 lg:dark:hover:text-white dark:hover:bg-gray-700 dark:hover:text-white lg:dark:hover:bg-transparent dark:border-gray-700">All Coloring Pages</a>
                    </li>
                    <li>
                        <a href="/about" class="block py-2 pr-4 pl-3 text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-white lg:border-0 lg:hover:text-primary lg:p-0 dark:text-gray-400 lg:dark:hover:text-white dark:hover:bg-gray-700 dark:hover:text-white lg:dark:hover:bg-transparent dark:border-gray-700">About</a>
                    </li>
                </ul>
                </div>
            </div>
        </div>
    </nav>
</header>

<style>
  /* Logo 样式 */
  .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo-image {
    height: auto;
    width: auto;
    max-width: 40px;
    max-height: 40px;
    object-fit: contain;
  }

  /* SVG 特定样式 - 只针对 logo 中的 SVG */
  .logo-image :global(svg) {
    width: 100%;
    height: 100%;
  }
</style>

<script>
  // 导入 Flowbite 的初始化函数
  import { initFlowbite } from 'flowbite';

  // 在页面加载时初始化 Flowbite
  document.addEventListener('DOMContentLoaded', () => {
    initFlowbite();
  });
</script>