---
// Hero 组件
// 使用Cloudflare R2存储的图片
const CLOUDFLARE_R2_PUBLIC_URL = 'https://static.printablecoloringhub.com';

// 定义轮播图图片数组
const heroImages = [
  {
    src: `${CLOUDFLARE_R2_PUBLIC_URL}/hero_img/hero1.webp`,
    alt: 'Coloring page hero image 1'
  },
  {
    src: `${CLOUDFLARE_R2_PUBLIC_URL}/hero_img/hero2.webp`,
    alt: 'Coloring page hero image 2'
  },
  {
    src: `${CLOUDFLARE_R2_PUBLIC_URL}/hero_img/hero3.webp`,
    alt: 'Coloring page hero image 3'
  },
  {
    src: `${CLOUDFLARE_R2_PUBLIC_URL}/hero_img/hero4.webp`,
    alt: 'Coloring page hero image 4'
  },
  {
    src: `${CLOUDFLARE_R2_PUBLIC_URL}/hero_img/hero5.webp`,
    alt: 'Coloring page hero image 5'
  }
];

// 为每个图片生成优化的URL
const optimizedHeroImages = heroImages.map(image => {
  // 获取文件名
  const filename = image.src.split('/').pop();

  // 移动设备尺寸 (小屏幕)
  const mobileSrc = `${CLOUDFLARE_R2_PUBLIC_URL}/cdn-cgi/image/width=600,height=480,fit=cover,quality=80,format=auto/hero_img/${filename}`;

  // 平板设备尺寸 (中等屏幕)
  const tabletSrc = `${CLOUDFLARE_R2_PUBLIC_URL}/cdn-cgi/image/width=800,height=640,fit=cover,quality=85,format=auto/hero_img/${filename}`;

  // 桌面设备尺寸 (大屏幕)
  const desktopSrc = `${CLOUDFLARE_R2_PUBLIC_URL}/cdn-cgi/image/width=1020,height=816,fit=cover,quality=90,format=auto/hero_img/${filename}`;

  return {
    mobileSrc,
    tabletSrc,
    desktopSrc,
    originalSrc: image.src,
    alt: image.alt
  };
});

// 轮播图配置
const interval = 5000; // 默认5秒切换一次
const showDots = true;
---

<section class="bg-white dark:bg-gray-900">
    <div class="max-w-screen-2xl px-4 py-8 mx-auto lg:py-16">
        <div class="grid items-center gap-8 mb-8 lg:mb-16 lg:gap-12 lg:grid-cols-12">
            <div class="col-span-6 text-center sm:mb-6 lg:text-left lg:mb-0">

                <h1 class="mb-4 text-4xl font-extrabold leading-none tracking-tight text-gray-900 md:text-5xl xl:text-6xl dark:text-white">Free Printable Coloring Pages for Everyone</h1>
                <p class="max-w-3xl mx-auto mb-6 font-light text-gray-500 lg:mx-0 xl:mb-8 md:text-lg xl:text-xl dark:text-gray-400">Discover hundreds of beautiful coloring pages for kids and adults. Download, print, and bring your creativity to life with our high-quality designs.</p>

                <!-- 搜索框 - 全局绿色样式 -->
                <div class="max-w-3xl mx-auto lg:mx-0 mb-6">
                  <form action="/coloring-pages" method="get" class="flex items-center">
                    <div class="relative w-full">
                      <input
                        type="search"
                        name="search"
                        class="bg-white border-2 border-[var(--color-primary)] text-gray-900 text-base rounded-lg focus:ring-[var(--color-primary)] focus:border-[var(--color-primary-hover)] block w-full p-3 md:p-4 shadow-md transition-all duration-200 hover:shadow-lg"
                        placeholder="Search for coloring pages..."
                      />
                    </div>
                    <button type="submit" class="btn btn-primary p-3 md:p-4 ml-2 text-base font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center">
                      <span>Search</span>
                    </button>
                  </form>
                </div>

                <!-- 分类和全部页面按钮 -->
                <div class="max-w-3xl mx-auto lg:mx-0 flex flex-wrap gap-4 justify-center lg:justify-start">
                  <a href="/categories" class="btn btn-primary inline-flex items-center px-6 py-3 text-base font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200">
                    <span>Categories</span>
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </a>
                  <a href="/coloring-pages" class="btn btn-primary inline-flex items-center px-6 py-3 text-base font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200">
                    <span>All Coloring Pages</span>
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                  </a>
                </div>
            </div>
            <div class="col-span-6">
                <div class="hero-carousel-container">
                  <div class="hero-carousel" data-interval={interval}>
                    {optimizedHeroImages.map((image, index) => (
                      <div class={`carousel-slide ${index === 0 ? 'active' : ''}`} data-index={index}>
                        <picture>
                          <source media="(max-width: 640px)" srcset={image.mobileSrc} />
                          <source media="(max-width: 1024px)" srcset={image.tabletSrc} />
                          <source media="(min-width: 1025px)" srcset={image.desktopSrc} />
                          <img
                            src={image.originalSrc}
                            alt={image.alt}
                            width="1020"
                            height="816"
                            loading={index === 0 ? "eager" : "lazy"}
                            class="carousel-image"
                          />
                        </picture>
                      </div>
                    ))}

                    {/* 指示点导航 */}
                    {showDots && (
                      <div class="carousel-dots">
                        {optimizedHeroImages.map((_, index) => (
                          <button
                            class={`carousel-dot ${index === 0 ? 'active' : ''}`}
                            data-index={index}
                            aria-label={`Go to slide ${index + 1}`}
                          ></button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
            </div>
        </div>
        <div class="grid gap-8 sm:gap-12 md:grid-cols-3">
            <div class="flex justify-center">
                <svg class="w-6 h-6 mr-3 icon-primary shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd"></path></svg>
                <div>
                    <h3 class="mb-1 text-lg font-semibold leading-tight text-gray-900 dark:text-white">Multiple Categories</h3>
                    <p class="font-light text-gray-500 dark:text-gray-400">Browse coloring pages by categories like animals, holidays, seasons, and more to find the perfect design.</p>
                </div>
            </div>
            <div class="flex justify-center">
                <svg class="w-6 h-6 mr-3 icon-primary shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path></svg>
                <div>
                    <h3 class="mb-1 text-lg font-semibold leading-tight text-gray-900 dark:text-white">Free Downloads</h3>
                    <p class="font-light text-gray-500 dark:text-gray-400">Download all our coloring pages for free in both PDF and PNG formats for easy printing and digital coloring.</p>
                </div>
            </div>
            <div class="flex justify-center">
                <svg class="w-6 h-6 mr-3 icon-primary shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11 4a1 1 0 10-2 0v4a1 1 0 102 0V7zm-3 1a1 1 0 10-2 0v3a1 1 0 102 0V8zM8 9a1 1 0 00-2 0v2a1 1 0 102 0V9z" clip-rule="evenodd"></path></svg>
                <div>
                    <h3 class="mb-1 text-lg font-semibold leading-tight text-gray-900 dark:text-white">Regular Updates</h3>
                    <p class="font-light text-gray-500 dark:text-gray-400">We add new coloring pages regularly, so check back often for fresh designs and seasonal content.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
  /* 轮播图样式 */
  .hero-carousel-container {
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
  }

  .hero-carousel {
    position: relative;
    width: 100%;
  }

  .carousel-slide {
    display: none;
    width: 100%;
    height: 100%;
    transition: opacity 0.5s ease;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
  }

  .carousel-slide.active {
    display: block;
    opacity: 1;
    position: relative;
  }

  .carousel-image {
    width: 100%;
    height: 100%;
    aspect-ratio: 5/4;
    object-fit: cover; /* 改为 cover 以确保图片填满容器 */
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  /* 箭头导航样式 */
  .carousel-arrows {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    pointer-events: none; /* 确保不会干扰轮播图的点击 */
    z-index: 10;
  }

  .carousel-arrow {
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--color-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    pointer-events: auto; /* 使按钮可点击 */
    opacity: 0.9;
  }

  .carousel-arrow:hover {
    background-color: white;
    color: var(--color-primary-hover);
    transform: scale(1.05);
    opacity: 1;
  }

  .carousel-arrow:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(35, 168, 52, 0.3);
  }

  /* 指示点导航样式 */
  .carousel-dots {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 10px;
    z-index: 10;
  }

  .carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .carousel-dot.active {
    background-color: var(--color-primary);
    border-color: white;
    transform: scale(1.2);
  }

  .carousel-dot:hover {
    background-color: rgba(255, 255, 255, 0.9);
  }

  .carousel-dot:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(35, 168, 52, 0.3);
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .carousel-arrow {
      width: 36px;
      height: 36px;
    }

    .carousel-dots {
      bottom: 12px;
    }

    .carousel-dot {
      width: 10px;
      height: 10px;
    }
  }

  /* 在小屏幕上进一步缩小控制元素 */
  @media (max-width: 480px) {
    .carousel-arrow {
      width: 32px;
      height: 32px;
    }

    .carousel-arrows {
      padding: 0 8px;
    }

    .carousel-dots {
      bottom: 8px;
      gap: 8px;
    }

    .carousel-dot {
      width: 8px;
      height: 8px;
      border-width: 1px;
    }
  }
</style>

<script>
  // 轮播图功能实现
  class Carousel {
    container: HTMLElement;
    slides: HTMLElement[];
    dots: HTMLElement[];
    currentIndex: number = 0;
    interval: number;
    timer: number | null = null;

    constructor(container: HTMLElement) {
      this.container = container;
      this.slides = Array.from(container.querySelectorAll('.carousel-slide'));
      this.dots = Array.from(container.querySelectorAll('.carousel-dot'));
      this.interval = parseInt(container.dataset.interval || '5000', 10);

      this.init();
    }

    init() {
      // 添加事件监听器
      this.dots.forEach(dot => {
        dot.addEventListener('click', () => {
          const index = parseInt(dot.dataset.index || '0', 10);
          this.goToSlide(index);
        });
      });

      // 启动自动播放
      this.startAutoplay();

      // 鼠标悬停时暂停，移出时继续
      this.container.addEventListener('mouseenter', () => this.stopAutoplay());
      this.container.addEventListener('mouseleave', () => this.startAutoplay());
    }

    goToSlide(index: number) {
      // 移除当前活动幻灯片的active类
      this.slides[this.currentIndex].classList.remove('active');
      if (this.dots.length > 0) {
        this.dots[this.currentIndex].classList.remove('active');
      }

      // 更新当前索引
      this.currentIndex = index;

      // 如果索引超出范围，重置为第一张或最后一张
      if (this.currentIndex < 0) {
        this.currentIndex = this.slides.length - 1;
      } else if (this.currentIndex >= this.slides.length) {
        this.currentIndex = 0;
      }

      // 添加新的活动幻灯片的active类
      this.slides[this.currentIndex].classList.add('active');
      if (this.dots.length > 0) {
        this.dots[this.currentIndex].classList.add('active');
      }
    }

    prevSlide() {
      this.goToSlide(this.currentIndex - 1);
    }

    nextSlide() {
      this.goToSlide(this.currentIndex + 1);
    }

    startAutoplay() {
      if (this.timer === null) {
        this.timer = window.setInterval(() => {
          this.nextSlide();
        }, this.interval);
      }
    }

    stopAutoplay() {
      if (this.timer !== null) {
        window.clearInterval(this.timer);
        this.timer = null;
      }
    }
  }

  // 初始化页面上的所有轮播图
  document.addEventListener('DOMContentLoaded', () => {
    const carousels = document.querySelectorAll('.hero-carousel');
    carousels.forEach(carousel => {
      new Carousel(carousel as HTMLElement);
    });
  });
</script>
