// Cloudflare配置文件

// 从环境变量获取Cloudflare R2配置
export const CLOUDFLARE_R2_ACCOUNT_ID = import.meta.env.CLOUDFLARE_R2_ACCOUNT_ID;
export const CLOUDFLARE_R2_ACCESS_KEY_ID = import.meta.env.CLOUDFLARE_R2_ACCESS_KEY_ID;
export const CLOUDFLARE_R2_SECRET_ACCESS_KEY = import.meta.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY;
export const CLOUDFLARE_R2_BUCKET_NAME = import.meta.env.CLOUDFLARE_R2_BUCKET_NAME || 'uslocal';
export const CLOUDFLARE_R2_PUBLIC_URL = import.meta.env.CLOUDFLARE_R2_PUBLIC_URL || 'https://static.printablecoloringhub.com';

// Cloudflare R2基本路径
export const CLOUDFLARE_R2_BASE_PATH = 'coloring-pages';

// Cloudflare 图像优化配置
export const CLOUDFLARE_IMAGES = {
  // 图片变换预设
  presets: {
    // 缩略图预设
    thumbnail: {
      width: 300,
      height: 300,
      fit: 'cover',
      quality: 80,
      format: 'auto'
    },
    // 卡片预设
    card: {
      width: 500,
      height: 500,
      fit: 'cover',
      quality: 80,
      format: 'auto'
    },
    // 详情页预设
    detail: {
      width: 800,
      height: 800,
      fit: 'contain',
      quality: 85,
      format: 'auto'
    },
    // 全尺寸预设
    full: {
      width: 1200,
      height: 1200,
      fit: 'contain',
      quality: 90,
      format: 'auto'
    }
  }
};

/**
 * 构建Cloudflare R2 URL，支持图像大小调整
 * @param assetFolder 资源文件夹名称
 * @param filename 文件名
 * @param options 图片优化选项
 * @returns Cloudflare R2 URL
 */
export function buildR2Url(
  assetFolder: string,
  filename: string,
  options: {
    width?: number;
    height?: number;
    fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
    quality?: number;
    format?: 'auto' | 'webp' | 'avif' | 'jpeg' | 'png';
    dpr?: number;
    gravity?: 'auto' | 'center' | 'north' | 'south' | 'east' | 'west';
  } = {}
): string {
  // 确保assetFolder和filename都存在
  if (!assetFolder || !filename) {
    console.error('Asset folder or filename is missing', { assetFolder, filename });
    return '';
  }

  // 移除可能的前导斜杠
  const cleanAssetFolder = assetFolder.replace(/^\/+/, '');
  const cleanFilename = filename.replace(/^\/+/, '');

  // 构建基本URL路径
  const imagePath = `${CLOUDFLARE_R2_BASE_PATH}/${cleanAssetFolder}/${cleanFilename}`;

  // 如果没有优化选项，直接返回基本URL
  if (Object.keys(options).length === 0) {
    return `${CLOUDFLARE_R2_PUBLIC_URL}/${imagePath}`;
  }

  // 构建Cloudflare图像大小调整参数
  const imageParams = [];

  if (options.width) imageParams.push(`width=${options.width}`);
  if (options.height) imageParams.push(`height=${options.height}`);
  if (options.fit) imageParams.push(`fit=${options.fit}`);
  if (options.quality) imageParams.push(`quality=${options.quality}`);
  if (options.format) imageParams.push(`format=${options.format}`);
  if (options.dpr) imageParams.push(`dpr=${options.dpr}`);
  if (options.gravity) imageParams.push(`gravity=${options.gravity}`);

  // 确保至少有一个参数
  if (imageParams.length === 0) {
    return `${CLOUDFLARE_R2_PUBLIC_URL}/${imagePath}`;
  }

  // 构建最终URL
  return `${CLOUDFLARE_R2_PUBLIC_URL}/cdn-cgi/image/${imageParams.join(',')}/${imagePath}`;
}


