// 性能优化配置
export const PERFORMANCE_CONFIG = {
  // 首页显示的最大图片数量
  homepage: {
    maxPopularPages: 32,   // 热门页面最多显示32个
    maxNewPages: 4,        // 新页面最多显示4个
    maxCategories: 6,      // 分类最多显示6个
  },

  // 懒加载配置
  lazyLoading: {
    threshold: 0.1,        // 当元素10%可见时开始加载
    rootMargin: '100px',   // 提前100px开始加载
    eagerLoadCount: 4,     // 前4张图片立即加载
  },

  // 图片优化配置
  images: {
    // 卡片图片尺寸
    card: {
      width: 500,
      height: 500,
      quality: 80,
      format: 'auto',
    },

    // 缩略图尺寸
    thumbnail: {
      width: 300,
      height: 300,
      quality: 75,
      format: 'webp',
    },

    // 占位符尺寸
    placeholder: {
      width: 20,
      height: 20,
      quality: 60,
      format: 'webp',
    },
  },

  // 预加载配置
  preload: {
    // 关键资源预加载
    criticalImages: 1,     // 预加载第一张关键图片
    heroImages: 1,         // 预加载第一张Hero图片
  },

  // 缓存配置
  cache: {
    // 图片缓存时间（秒）
    imageMaxAge: 31536000, // 1年
    // 页面缓存时间（秒）
    pageMaxAge: 3600,      // 1小时
  },
};

// 获取优化后的图片加载策略
export function getImageLoadingStrategy(index: number, priority: boolean = false): 'eager' | 'lazy' {
  if (priority || index < PERFORMANCE_CONFIG.lazyLoading.eagerLoadCount) {
    return 'eager';
  }
  return 'lazy';
}

// 获取图片质量设置
export function getImageQuality(preset: 'card' | 'thumbnail' | 'placeholder'): number {
  return PERFORMANCE_CONFIG.images[preset].quality;
}

// 获取图片尺寸设置
export function getImageDimensions(preset: 'card' | 'thumbnail' | 'placeholder'): { width: number; height: number } {
  const config = PERFORMANCE_CONFIG.images[preset];
  return {
    width: config.width,
    height: config.height,
  };
}

// 检查是否应该预加载图片
export function shouldPreloadImage(index: number, type: 'critical' | 'hero'): boolean {
  const maxPreload = type === 'critical'
    ? PERFORMANCE_CONFIG.preload.criticalImages
    : PERFORMANCE_CONFIG.preload.heroImages;

  return index < maxPreload;
}

// 生成性能优化的HTML属性
export function getPerformanceAttributes(index: number, priority: boolean = false) {
  const loading = getImageLoadingStrategy(index, priority);
  const shouldPreload = shouldPreloadImage(index, 'critical');

  return {
    loading,
    decoding: 'async' as const,
    fetchpriority: shouldPreload ? 'high' as const : 'auto' as const,
  };
}
