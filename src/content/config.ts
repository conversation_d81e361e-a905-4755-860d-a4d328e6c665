import { defineCollection, z } from 'astro:content';

// 定义分类结构的schema
const categorySchema = z.object({
  main: z.string(),  // 主分类
  sub: z.string().optional(),  // 子分类（可选）
  subsub: z.string().optional(),  // 孙分类（可选）
});

// 定义 Coloring Pages 集合的 schema
const coloringPagesCollection = defineCollection({
  type: 'content',
  // 同时支持 .md 和 .mdx 文件
  schema: z.object({
    title: z.string(),
    id: z.string(),  // 添加 ID 字段，用于关联原始文件

    // 分类系统（结构化）
    categoryInfo: categorySchema, // 结构化分类信息

    // 集合系统
    collections: z.array(z.string()).optional(), // 集合名称数组，例如：['Holidays', 'Christmas']

    // 资源文件夹
    assetFolder: z.string(), // 资源文件夹名称

    // 资源文件会通过assetFolder自动关联，不需要单独指定文件名

    // 其他字段
    description: z.string().optional(), // 详情页描述文案
    tags: z.array(z.string()).optional(), // 标签系统
    popular: z.boolean().optional().default(false), // 是否热门
    dateAdded: z.date().optional().default(() => new Date()), // 创建时间
    featured: z.boolean().optional().default(false), // 是否推荐
    premium: z.boolean().optional().default(false), // 是否付费内容
  }),
});

// 导出集合配置
export const collections = {
  'coloring-pages': coloringPagesCollection,
};
