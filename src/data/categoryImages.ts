// 为每个主分类定义代表性图片
// 这些图片将在分类页面上显示，使用实际分类内容作为代表图片

// 使用Cloudflare R2 URL
const CLOUDFLARE_R2_PUBLIC_URL = 'https://static.printablecoloringhub.com';
const CLOUDFLARE_R2_BASE_PATH = 'coloring-pages';

// 构建R2 URL函数
function buildR2Url(assetFolder: string, filename: string): string {
  return `${CLOUDFLARE_R2_PUBLIC_URL}/${CLOUDFLARE_R2_BASE_PATH}/${assetFolder}/${filename}`;
}

// 定义图片URL
const animalsImage = buildR2Url('Adorable-Basset-Hound', 'monochrome.png');
const charactersImage = buildR2Url('Hello-Kitty-Wedding-Bliss', 'monochrome.png');
const holidaysImage = buildR2Url('Mickey-Minnie-Christmas-Wreath', 'monochrome.png');
const natureImage = buildR2Url('Sunny-Sunflower-Field', 'monochrome.png');
const peopleImage = buildR2Url('<PERSON>-<PERSON>-Singer', 'monochrome.png');
const foodImage = buildR2Url('Kawaii-Ice-Cream-Cone', 'monochrome.png');
const artDesignImage = buildR2Url('Folk-Art-Flower-Vase', 'monochrome.png');
const fantasyImage = buildR2Url('Magical-Unicorn-Pony', 'monochrome.png');
const seasonsImage = buildR2Url('Vintage-Pumpkin-Truck', 'monochrome.png');
const vehiclesImage = buildR2Url('Sleek-Sports-Car', 'monochrome.png');
const defaultImage = `${CLOUDFLARE_R2_PUBLIC_URL}/default-colored-reference.png`;

// 定义分类图片映射
export const categoryImages: Record<string, string> = {
  // 主要分类
  'Animals': animalsImage,
  'Characters': charactersImage,
  'Holidays': holidaysImage,
  'Nature': natureImage,
  'People': peopleImage,
  'Food': foodImage,
  'Food & Drink': foodImage,
  'Art & Design': artDesignImage,
  'Fantasy': fantasyImage,
  'Fantasy & Mythical': fantasyImage,
  'Seasons': seasonsImage,
  'Vehicles': vehiclesImage,

  // 其他分类
  'Activities': defaultImage,
  'Arts & Entertainment': artDesignImage,
  'Cartoon & Anime': charactersImage,
  'Cartoon Characters': charactersImage,
  'Cartoons': charactersImage,
  'Cartoons & Characters': charactersImage,
  'Cartoons & Games': charactersImage,
  'Cartoons & Movies': charactersImage,
  'Cartoons & TV Shows': charactersImage,
  'Celebrities': peopleImage,
  'Dinosaurs': animalsImage,
  'Everyday Life': peopleImage,
  'Everyday Objects': defaultImage,
  'Fairy Tales & Fantasy': fantasyImage,
  'Family & People': peopleImage,
  'Family & Relationships': peopleImage,
  'Famous People': peopleImage,
  'Fictional Characters': charactersImage,
  'Games': defaultImage,
  'Games & Comics': charactersImage,
  'Games & Entertainment': defaultImage,
  'Games & Fantasy': fantasyImage,
  'Home & Decor': defaultImage,
  'Home & Living': defaultImage,
  'Household': defaultImage,
  'Household Items': defaultImage,
  'Indoor Scenes': defaultImage,
  'Interiors': defaultImage,
  'Lifestyle': peopleImage,
  'Mandalas': artDesignImage,
  'Objects': defaultImage,
  'Objects & Places': defaultImage,
  'Occasions': holidaysImage,
  'Outdoor Activities': natureImage,
  'Pop Culture': charactersImage,
  'Rooms & Spaces': defaultImage,
  'Scenes': defaultImage,
  'Scenes & Scenery': natureImage,
  'School': defaultImage,
  'Sci-Fi': fantasyImage,
  'Seasonal': seasonsImage,
  'Sports': defaultImage,
  'Still Life': artDesignImage,
  'Superheroes': charactersImage,
  'Themes': defaultImage,
  'Transportation': vehiclesImage,
  'TV & Movie Characters': charactersImage,

  // 默认图片
  'Other': defaultImage,
  'default': defaultImage
};

// 获取分类图片的函数
export function getCategoryImage(categoryName: string): string {
  return categoryImages[categoryName] || categoryImages['default'];
}
