/// <reference types="astro/client" />

// 为 .astro 文件声明类型
declare module '*.astro' {
  import { AstroComponentFactory } from 'astro/runtime/server/index.js';
  const Component: AstroComponentFactory;
  export default Component;
}

// 为 .ts 文件声明类型
declare module '*.ts';

// 为项目中的模块声明类型
declare module '@/layouts/Layout.astro';
declare module '@/components/ColoringCard.astro';
declare module '@/components/CloudflareColoringCard.astro';
declare module '@/components/CloudflareImage.astro';
declare module '@/components/CloudflareDetailImage.astro';
declare module '@/components/CloudflareDownloadButton.astro';
declare module '@/data/coloringPages';

// 为环境变量声明类型
interface ImportMetaEnv {
  readonly CLOUDFLARE_R2_ACCOUNT_ID: string;
  readonly CLOUDFLARE_R2_ACCESS_KEY_ID: string;
  readonly CLOUDFLARE_R2_SECRET_ACCESS_KEY: string;
  readonly CLOUDFLARE_R2_BUCKET_NAME: string;
  readonly CLOUDFLARE_R2_PUBLIC_URL: string;

  // Cloudflare Images 环境变量
  readonly CLOUDFLARE_IMAGES_ACCOUNT_ID: string;
  readonly CLOUDFLARE_IMAGES_ACCOUNT_HASH: string;
  readonly CLOUDFLARE_IMAGES_DELIVERY_URL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 为 ColoringPage 类型声明
declare module '@/types/coloringPage' {
  import type { ImageMetadata } from 'astro:assets';

  // 定义分类结构
  export interface Category {
    main: string;       // 主分类
    sub?: string;       // 子分类（可选）
    subsub?: string;    // 孙分类（可选）
  }

  export interface ColoringPage {
    id: string;
    slug: string;
    title: string;

    // 分类系统（结构化）
    categoryInfo: Category; // 结构化分类信息

    // 集合系统
    collections?: string[]; // 集合名称数组，例如：['Holidays', 'Christmas']

    // 资源文件夹
    assetFolder: string; // 资源文件夹名称

    // 资源文件导入对象（由 [id].astro 页面动态导入）
    monochromePngImage?: ImageMetadata;  // 导入的黑白 PNG 图像对象
    coloredPngImage?: ImageMetadata;     // 导入的彩色参考图对象

    // 其他字段
    dateAdded?: string | Date;
    popular?: boolean;
    featured?: boolean;
    premium?: boolean;
    description?: string;
    tags?: string[];
  }
}
