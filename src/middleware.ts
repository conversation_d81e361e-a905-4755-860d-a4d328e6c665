import { defineMiddleware } from 'astro:middleware';

export const onRequest = defineMiddleware(async (context, next) => {
  // 获取当前请求的URL路径
  const url = new URL(context.request.url);
  const pathname = url.pathname;

  // 重定向规则
  const redirects = {
    '/all-pages': '/coloring-pages',
  };

  // 检查是否需要重定向
  if (redirects[pathname]) {
    return new Response('', {
      status: 301, // 永久重定向
      headers: {
        'Location': redirects[pathname]
      }
    });
  }

  // 没有匹配的重定向规则，继续处理请求
  return next();
});
