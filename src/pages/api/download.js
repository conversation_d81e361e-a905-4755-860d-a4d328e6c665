// 下载代理服务器
// 这个服务器会添加正确的Content-Disposition头，强制浏览器下载文件而不是在浏览器中打开它

// 使用默认导出而不是命名导出
export default async function({ request }) {
  try {
    // 从查询参数中获取URL和文件名
    const url = new URL(request.url);
    const fileUrl = url.searchParams.get('url');
    let fileName = url.searchParams.get('fileName');

    // 验证URL
    if (!fileUrl) {
      return new Response('Missing URL parameter', { status: 400 });
    }

    // 如果没有提供文件名，从URL中提取
    if (!fileName) {
      const urlParts = fileUrl.split('/');
      fileName = urlParts[urlParts.length - 1];
    }

    // 获取文件类型
    const fileType = fileName.split('.').pop().toLowerCase();

    // 设置适当的Content-Type
    let contentType = 'application/octet-stream';
    if (fileType === 'pdf') {
      contentType = 'application/pdf';
    } else if (fileType === 'png') {
      contentType = 'image/png';
    } else if (fileType === 'jpg' || fileType === 'jpeg') {
      contentType = 'image/jpeg';
    } else if (fileType === 'webp') {
      contentType = 'image/webp';
    }

    // 获取文件
    const response = await fetch(fileUrl);

    if (!response.ok) {
      return new Response(`Failed to fetch file: ${response.statusText}`, { status: response.status });
    }

    // 获取文件内容
    const fileData = await response.arrayBuffer();

    // 创建新的响应，添加正确的头
    return new Response(fileData, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': fileData.byteLength.toString(),
        'Cache-Control': 'no-cache'
      }
    });
  } catch (error) {
    console.error('Download proxy error:', error);
    return new Response(`Error processing download: ${error.message}`, { status: 500 });
  }
}
