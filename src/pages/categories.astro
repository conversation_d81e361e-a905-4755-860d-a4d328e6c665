---
import Layout from '@/layouts/Layout.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import CategoryCard from '@/components/CategoryCard.astro';
import { getCategoriesWithSubcategories } from '@/data/coloringPages';

// 获取带子分类的分类列表
const categories = await getCategoriesWithSubcategories();

// 定义主要分类组
const mainCategories = [
  { id: 'all', name: 'All Categories' },
  { id: 'characters', name: 'Characters' },
  { id: 'animals', name: 'Animals' },
  { id: 'holidays', name: 'Holidays' },
  { id: 'nature', name: 'Nature' },
  { id: 'people', name: 'People' },
  { id: 'food', name: 'Food' },
  { id: 'art', name: 'Art & Design' },
  { id: 'fantasy', name: 'Fantasy' },
  { id: 'seasons', name: 'Seasons' },
  { id: 'vehicles', name: 'Vehicles' },
  { id: 'other', name: 'Other' }
];

// 将分类按名称分组，而不是主题
const groupedCategories = {
  characters: categories.filter((cat: any) => cat.name === 'Characters'),
  animals: categories.filter((cat: any) => cat.name === 'Animals'),
  holidays: categories.filter((cat: any) => cat.name === 'Holidays'),
  nature: categories.filter((cat: any) => cat.name === 'Nature'),
  people: categories.filter((cat: any) => cat.name === 'People'),
  food: categories.filter((cat: any) => cat.name === 'Food' || cat.name === 'Food & Drink'),
  art: categories.filter((cat: any) => cat.name === 'Art & Design' || cat.name === 'Arts & Entertainment'),
  fantasy: categories.filter((cat: any) => cat.name === 'Fantasy' || cat.name === 'Fantasy & Mythical'),
  seasons: categories.filter((cat: any) => cat.name === 'Seasons' || cat.name === 'Seasonal'),
  vehicles: categories.filter((cat: any) => cat.name === 'Vehicles' || cat.name === 'Transportation'),
  other: categories.filter((cat: any) =>
    !['Characters', 'Animals', 'Holidays', 'Nature', 'People', 'Food', 'Food & Drink',
      'Art & Design', 'Arts & Entertainment', 'Fantasy', 'Fantasy & Mythical',
      'Seasons', 'Seasonal', 'Vehicles', 'Transportation'].includes(cat.name)
  )
};

// 计算总分类数
const totalCategories = categories.length;
---

<Layout title="Categories - PrintableColoringHub" description="Browse our coloring pages by category. Find animals, Disney, superheroes, holidays, and more.">
  <div class="container-custom py-8">
    <div class="mb-6">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Categories", isActive: true }
        ]}
      />
    </div>

    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
      <h1 class="text-3xl font-bold mb-2 md:mb-0">Browse by Category</h1>
      <p class="text-gray-600">{totalCategories} categories to explore</p>
    </div>

    <!-- 搜索和过滤 -->
    <div class="mb-10">
      <div class="relative max-w-xl mx-auto mb-8">
        <input
          type="text"
          id="category-search"
          placeholder="Search categories..."
          class="w-full px-6 py-4 border border-gray-200 rounded-full shadow-sm focus:ring-2 focus:ring-primary focus:border-primary text-lg"
        />
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>

      <!-- 分类导航 -->
      <div class="flex flex-wrap justify-center gap-4 mb-6">
        {mainCategories.map((category, index) => (
          <button
            class={`category-filter-btn px-6 py-3 rounded-full transition-colors font-medium ${
              index === 0
                ? "active bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20"
                : "bg-gray-100 text-gray-800 hover:bg-gray-200"
            }`}
            data-filter={category.id}
          >
            {category.name}
          </button>
        ))}
      </div>
    </div>

    <!-- 主要分类展示 -->
    <div id="categories-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
      {categories.map((category: any) => {
        // 确定该分类属于哪个组
        let categoryId = 'other';

        // 根据分类名称确定其ID
        if (category.name === 'Characters') categoryId = 'characters';
        else if (category.name === 'Animals') categoryId = 'animals';
        else if (category.name === 'Holidays') categoryId = 'holidays';
        else if (category.name === 'Nature') categoryId = 'nature';
        else if (category.name === 'People') categoryId = 'people';
        else if (category.name === 'Food' || category.name === 'Food & Drink') categoryId = 'food';
        else if (category.name === 'Art & Design' || category.name === 'Arts & Entertainment') categoryId = 'art';
        else if (category.name === 'Fantasy' || category.name === 'Fantasy & Mythical') categoryId = 'fantasy';
        else if (category.name === 'Seasons' || category.name === 'Seasonal') categoryId = 'seasons';
        else if (category.name === 'Vehicles' || category.name === 'Transportation') categoryId = 'vehicles';

        // 收集所有子分类名称用于搜索
        const subcategoryNames = category.subcategories.map((sub: any) => sub.name.toLowerCase()).join(' ');

        return (
          <div
            class="category-item"
            data-category-name={category.name.toLowerCase()}
            data-category-id={categoryId}
            data-subcategories={subcategoryNames}
          >
            <CategoryCard
              category={category}
              searchTerm=""
            />
          </div>
        );
      })}
    </div>

    <!-- 无结果提示 -->
    <div id="no-results" class="hidden text-center py-12">
      <p class="text-xl text-gray-600">No categories found matching your search.</p>
      <button id="clear-search" class="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors">
        Clear Search
      </button>
    </div>
  </div>
</Layout>

<script>
  // 搜索和过滤功能
  document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('category-search') as HTMLInputElement;
    const categoryItems = document.querySelectorAll('.category-item');
    const categoriesGrid = document.getElementById('categories-grid');
    const noResults = document.getElementById('no-results');
    const clearSearchBtn = document.getElementById('clear-search');
    const filterButtons = document.querySelectorAll('.category-filter-btn');

    if (searchInput && categoryItems && categoriesGrid && noResults && clearSearchBtn) {
      let currentFilter = 'all'; // 默认显示所有分类

      // 更新按钮状态的函数
      const updateButtonState = (filter: string) => {
        filterButtons.forEach((btn) => {
          const btnFilter = btn.getAttribute('data-filter') || 'all';
          if (btnFilter === filter) {
            btn.classList.add('active');
            btn.classList.add('bg-primary/10', 'text-primary', 'border', 'border-primary/20');
            btn.classList.remove('bg-gray-100', 'text-gray-800');
          } else {
            btn.classList.remove('active');
            btn.classList.remove('bg-primary/10', 'text-primary', 'border', 'border-primary/20');
            btn.classList.add('bg-gray-100', 'text-gray-800');
          }
        });
      };

      // 过滤和搜索处理函数
      const filterAndSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        let visibleCount = 0;

        // 如果搜索框有内容，自动切换到"全部"分类
        if (searchTerm && currentFilter !== 'all') {
          currentFilter = 'all';
          updateButtonState('all');
        }

        categoryItems.forEach((item) => {
          const categoryName = item.getAttribute('data-category-name') || '';
          const categoryId = item.getAttribute('data-category-id') || '';
          const subcategories = item.getAttribute('data-subcategories') || '';

          // 检查是否匹配当前过滤器和搜索词
          const matchesFilter = currentFilter === 'all' || categoryId === currentFilter;

          // 搜索主分类名称和子分类名称
          const matchesSearch = !searchTerm ||
                               categoryName.includes(searchTerm) ||
                               subcategories.includes(searchTerm);

          const isVisible = matchesFilter && matchesSearch;

          (item as HTMLElement).style.display = isVisible ? 'block' : 'none';

          if (isVisible) {
            visibleCount++;
          }
        });

        // 显示或隐藏"无结果"消息
        if (visibleCount === 0) {
          categoriesGrid.classList.add('hidden');
          noResults.classList.remove('hidden');
        } else {
          categoriesGrid.classList.remove('hidden');
          noResults.classList.add('hidden');
        }
      };

      // 监听搜索输入
      searchInput.addEventListener('input', (e) => {
        // 如果搜索框被清空，保持当前选中的分类
        if ((e.target as HTMLInputElement).value === '' && currentFilter !== 'all') {
          // 不改变当前过滤器
        } else {
          filterAndSearch();
        }
      });

      // 清除搜索
      clearSearchBtn.addEventListener('click', () => {
        searchInput.value = '';
        // 不重置分类过滤器，保持当前选中的分类
        filterAndSearch();
        searchInput.focus();
      });

      // 分类过滤按钮
      filterButtons.forEach((button) => {
        button.addEventListener('click', () => {
          // 如果搜索框有内容，点击分类按钮时清空搜索
          if (searchInput.value.trim()) {
            searchInput.value = '';
          }

          // 更新当前过滤器
          currentFilter = button.getAttribute('data-filter') || 'all';

          // 更新按钮状态
          updateButtonState(currentFilter);

          // 应用过滤器
          filterAndSearch();
        });
      });

      // 初始化
      filterAndSearch();
    }
  });
</script>
