---
import Layout from '@/layouts/Layout.astro';
import ColoringCard from '@/components/ColoringCard.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import {
  getAllColoringPages,
  getCategoriesWithSubcategories,
  getTagsWithCounts,
  getColoringPagesByTag
} from '@/data/coloringPages';
import type { ColoringPage } from '@/types/coloringPage';
import { getImageUrl } from '@/utils/imageUtils';

// 获取URL参数
const { searchParams } = Astro.url;
const tagParams = searchParams.getAll('tag');
const searchQuery = searchParams.get('search');

// 获取所有分类及子分类（用于统计）
const categories = await getCategoriesWithSubcategories();

// 获取所有标签（只显示有多个内容的标签）
const allTags = await getTagsWithCounts();
const tags = allTags.filter((tag: { count: number }) => tag.count > 1);

// 根据URL参数筛选页面
let filteredPages: ColoringPage[] = [];
let allPages: ColoringPage[] = [];

// 获取所有页面用于统计和筛选
allPages = await getAllColoringPages();

// 应用筛选逻辑
if (tagParams.length > 0 || searchQuery) {
  let tagFilteredPages: ColoringPage[] = [];
  let searchFilteredPages: ColoringPage[] = [];

  // 处理标签筛选
  if (tagParams.length > 0) {
    const pagesByTag = await Promise.all(
      tagParams.map(async (tagSlug) => {
        // 将slug转换为标签名称
        const tag = allTags.find((t: { slug: string }) => t.slug === tagSlug);
        if (tag) {
          return await getColoringPagesByTag(tag.name);
        }
        return [];
      })
    );

    // 合并所有标签的页面并去重
    const allTagPages = pagesByTag.flat();
    const tagUniqueIds = new Set();
    tagFilteredPages = allTagPages.filter(page => {
      if (tagUniqueIds.has(page.id)) {
        return false;
      }
      tagUniqueIds.add(page.id);
      return true;
    });
  } else {
    tagFilteredPages = allPages;
  }

  // 处理搜索筛选
  if (searchQuery) {
    const query = searchQuery.toLowerCase().trim();
    const queryWords = query.split(/\s+/); // 将查询拆分为单词

    // 创建一个评分函数，根据匹配程度给页面评分
    const scorePageMatch = (page: ColoringPage): number => {
      let score = 0;

      // 标题匹配评分
      const title = page.title.toLowerCase();

      // 完全匹配标题 (最高分)
      if (title === query) {
        score += 100;
      }

      // 标题以查询开始
      if (title.startsWith(query)) {
        score += 50;
      }

      // 标题包含查询作为一个完整单词
      const titleWords = title.split(/\s+/);
      if (titleWords.includes(query)) {
        score += 40;
      }

      // 标题包含查询（部分匹配）
      if (title.includes(query)) {
        score += 20;
      }

      // 查询的所有单词都在标题中
      if (queryWords.every(word => title.includes(word))) {
        score += 30;
      }

      // 标签匹配评分
      if (page.tags && page.tags.length > 0) {
        // 完全匹配标签
        if (page.tags.some((tag: string) => tag.toLowerCase() === query)) {
          score += 80;
        }

        // 标签以查询开始
        if (page.tags.some((tag: string) => tag.toLowerCase().startsWith(query))) {
          score += 40;
        }

        // 标签包含查询
        if (page.tags.some((tag: string) => tag.toLowerCase().includes(query))) {
          score += 20;
        }

        // 查询的所有单词都在某个标签中
        if (queryWords.length > 1 &&
            page.tags.some((tag: string) =>
              queryWords.every(word => tag.toLowerCase().includes(word))
            )) {
          score += 30;
        }
      }

      // 分类匹配评分
      if (page.categoryInfo) {
        // 主分类完全匹配
        if (page.categoryInfo.category &&
            page.categoryInfo.category.toLowerCase() === query) {
          score += 70;
        }

        // 子分类完全匹配
        if (page.categoryInfo.subcategory &&
            page.categoryInfo.subcategory.toLowerCase() === query) {
          score += 60;
        }

        // 子子分类完全匹配
        if (page.categoryInfo.subsubcategory &&
            page.categoryInfo.subsubcategory.toLowerCase() === query) {
          score += 60;
        }

        // 分类包含查询（部分匹配，较低分数）
        const categoryText = [
          page.categoryInfo.category || '',
          page.categoryInfo.subcategory || '',
          page.categoryInfo.subsubcategory || ''
        ].join(' ').toLowerCase();

        if (categoryText.includes(query)) {
          score += 10;
        }
      }

      return score;
    };

    // 对所有页面进行评分并筛选
    const scoredPages = allPages.map(page => ({
      page,
      score: scorePageMatch(page)
    }));

    // 只保留评分大于0的页面，并按评分降序排序
    const filteredScoredPages = scoredPages
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score);

    // 提取排序后的页面
    searchFilteredPages = filteredScoredPages.map(item => item.page);
  } else {
    searchFilteredPages = allPages;
  }

  // 如果同时有标签和搜索筛选，取交集
  if (tagParams.length > 0 && searchQuery) {
    filteredPages = tagFilteredPages.filter(page =>
      searchFilteredPages.some(searchPage => searchPage.id === page.id)
    );
  } else if (tagParams.length > 0) {
    filteredPages = tagFilteredPages;
  } else {
    filteredPages = searchFilteredPages;
  }
} else {
  // 如果没有筛选参数，获取所有页面
  filteredPages = allPages;
}

// 计算统计数据
const totalPages = allPages.length;
const totalCategories = categories.length;

// 当前显示的页面数量
const displayedPages = filteredPages.length;
---

<Layout title="Coloring Pages - PrintableColoringHub" description="Browse our complete collection of free printable coloring pages for kids and adults. Download in PDF or PNG format.">
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "All Coloring Pages", isActive: true }
        ]}
      />
    </div>

    <!-- 顶部搜索和标签筛选区域 -->
    <div class="mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <!-- 搜索框 -->
        <div class="mb-6">
          <form action="/coloring-pages" method="get" class="flex items-center">
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                </svg>
              </div>
              <input
                type="search"
                name="search"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary focus:border-primary block w-full pl-10 p-2.5"
                placeholder="Search coloring pages..."
                value={searchParams.get('search') || ''}
              />
            </div>
            <button type="submit" class="p-2.5 ml-2 text-sm font-medium text-white bg-primary rounded-lg hover:bg-primary-hover focus:ring-4 focus:outline-none focus:ring-primary-light">
              <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
              </svg>
              <span class="sr-only">Search</span>
            </button>

            {/* 保留当前选中的标签 */}
            {tagParams.map(tag => (
              <input type="hidden" name="tag" value={tag} />
            ))}
          </form>
        </div>

        <!-- 标签筛选 -->
        <div>
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-semibold text-gray-800">Popular Tags</h3>
            {tagParams.length > 0 && (
              <a href="/coloring-pages" class="text-xs text-primary hover:text-primary-hover">
                Clear Tags
              </a>
            )}
          </div>

          <div class="relative">
            <!-- 初始显示的标签 - 移动端只显示两行，桌面端显示三行 -->
            <div id="visible-tags" class="flex flex-wrap gap-2 overflow-hidden transition-all duration-300" style="max-height: 5.5rem;">
              {tags.slice(0, 40).map((tag, index) => {
                const isSelected = tagParams.includes(tag.slug);

                // 根据索引轮换标签样式
                const colors = [
                  { bg: "bg-purple-100", text: "text-purple-700", hover: "hover:bg-purple-200", border: "border-purple-200" },
                  { bg: "bg-blue-100", text: "text-blue-700", hover: "hover:bg-blue-200", border: "border-blue-200" },
                  { bg: "bg-green-100", text: "text-green-700", hover: "hover:bg-green-200", border: "border-green-200" },
                  { bg: "bg-amber-100", text: "text-amber-700", hover: "hover:bg-amber-200", border: "border-amber-200" }
                ];

                const colorStyle = colors[index % colors.length];

                return (
                  <a
                    href={`/coloring-pages?${new URLSearchParams([
                      ...(searchParams.get('search') ? [['search', searchParams.get('search') || '']] : []),
                      ...tagParams.filter(t => t !== tag.slug).map(t => ['tag', t]),
                      ...(isSelected ? [] : [['tag', tag.slug]])
                    ]).toString()}`}
                    class={`cursor-pointer inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full transition-all duration-200 border shadow-sm ${
                      isSelected
                        ? `${colorStyle.bg} ${colorStyle.text} ${colorStyle.border}`
                        : `bg-gray-100 text-gray-700 hover:${colorStyle.bg.replace('bg-', '')} hover:${colorStyle.text.replace('text-', '')} border-transparent hover:${colorStyle.border}`
                    }`}
                  >
                    <span>#{tag.name}</span>
                    <span class={`ml-1.5 text-xs px-1.5 py-0.5 rounded-full bg-white ${
                      isSelected
                        ? `${colorStyle.text} border ${colorStyle.border}`
                        : 'text-gray-600 border border-gray-200'
                    }`}>{tag.count}</span>
                  </a>
                );
              })}
            </div>

            <!-- 渐变遮罩效果 -->
            <div id="tags-gradient" class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none"></div>

            <!-- 展开/收起按钮 -->
            <div class="flex justify-center mt-2">
              <button
                id="toggle-tags-btn"
                class="text-xs font-medium text-blue-600 hover:text-blue-800 flex items-center gap-1 bg-blue-50 hover:bg-blue-100 px-3 py-1.5 rounded-full transition-colors border border-blue-100 shadow-sm"
              >
                <span id="toggle-text">Show More Tags</span>
                <svg id="toggle-icon" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div>
      <div class="flex flex-wrap items-center justify-between mb-6">
        <div class="flex items-center">
          <h2 class="text-xl font-bold text-gray-800 mr-4">Browse Coloring Pages</h2>

          <!-- 统计信息 -->
          <div class="flex space-x-3 text-sm">
            <div class="bg-blue-50 rounded-lg px-2 py-1 border border-blue-100 flex items-center">
              <span class="font-bold text-blue-600 mr-1">{totalPages}</span>
              <span class="text-xs text-gray-700">Pages</span>
            </div>

            <div class="bg-purple-50 rounded-lg px-2 py-1 border border-purple-100 flex items-center">
              <span class="font-bold text-purple-600 mr-1">{totalCategories}</span>
              <span class="text-xs text-gray-700">Categories</span>
            </div>
          </div>
        </div>

        {/* 显示筛选结果计数 */}
        <div class="text-sm text-gray-600">
          {(tagParams.length > 0 || searchParams.get('search')) ? (
            <span>Showing <span class="font-semibold" id="result-count">{displayedPages}</span> of <span class="font-semibold">{totalPages}</span> coloring pages</span>
          ) : (
            <span>Showing <span class="font-semibold" id="result-count">{totalPages}</span> coloring pages</span>
          )}
        </div>
      </div>

      <!-- 活跃筛选器标签 -->
      {(tagParams.length > 0 || searchParams.get('search')) && (
        <div class="flex flex-wrap gap-2 mb-6">
          {/* 搜索筛选标签 */}
          {searchParams.get('search') && (
            <div class="flex items-center px-3 py-1.5 text-xs font-medium bg-blue-100 text-blue-700 rounded-full border border-blue-200 shadow-sm">
              <span>Search: {searchParams.get('search')}</span>
              <a href={`/coloring-pages?${new URLSearchParams(tagParams.map(t => ['tag', t])).toString()}`} class="ml-1.5 text-blue-700 hover:text-blue-900 bg-white p-1 rounded-full hover:bg-blue-50 transition-colors">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </a>
            </div>
          )}

          {/* 标签筛选标签 */}
          {tagParams.map((tagSlug, index) => {
            const tagName = allTags.find((t: any) => t.slug === tagSlug)?.name || tagSlug;

            // 根据索引轮换标签样式
            const colors = [
              { bg: "bg-purple-100", text: "text-purple-700", hover: "hover:bg-purple-50", border: "border-purple-200" },
              { bg: "bg-blue-100", text: "text-blue-700", hover: "hover:bg-blue-50", border: "border-blue-200" },
              { bg: "bg-green-100", text: "text-green-700", hover: "hover:bg-green-50", border: "border-green-200" },
              { bg: "bg-amber-100", text: "text-amber-700", hover: "hover:bg-amber-50", border: "border-amber-200" }
            ];

            const colorStyle = colors[index % colors.length];

            // 构建移除此筛选器后的URL
            const newParams = new URLSearchParams();
            // 保留搜索
            if (searchParams.get('search')) {
              newParams.append('search', searchParams.get('search') || '');
            }
            // 保留其他标签
            tagParams.filter((t: string) => t !== tagSlug).forEach((t: string) => newParams.append('tag', t));

            return (
              <div class={`flex items-center px-3 py-1.5 text-xs font-medium rounded-full border shadow-sm ${colorStyle.bg} ${colorStyle.text} ${colorStyle.border}`}>
                <span>#{tagName}</span>
                <a href={`/coloring-pages?${newParams.toString()}`} class={`ml-1.5 ${colorStyle.text} hover:text-opacity-80 bg-white p-1 rounded-full ${colorStyle.hover} transition-colors`}>
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </a>
              </div>
            );
          })}

          {/* 清除所有筛选器 */}
          <a href="/coloring-pages" class="flex items-center px-3 py-1.5 text-xs font-medium bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 border border-gray-200 hover:border-gray-300 transition-all duration-200 shadow-sm">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <span>Clear All</span>
          </a>
        </div>
      )}

      <p class="text-gray-700 mb-6">
        Browse our collection of free printable coloring pages. Find the perfect design for your coloring needs.
      </p>

        {/* 没有结果时显示提示 */}
        <div id="no-results-message" class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center mb-8" style="display: none;">
          <h3 class="text-lg font-medium text-blue-800 mb-2">No coloring pages found</h3>
          <p class="text-blue-700 mb-4">We couldn't find any coloring pages matching your selected filters.</p>
          <button type="button" id="clear-filters-btn" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-hover rounded-lg transition-colors duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Clear Filters
          </button>
        </div>

        {/* 服务器端初始渲染时没有结果的提示 */}
        {filteredPages.length === 0 && (
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center mb-8">
            <h3 class="text-lg font-medium text-blue-800 mb-2">No coloring pages found</h3>
            <p class="text-blue-700 mb-4">We couldn't find any coloring pages matching your selected filters.</p>
            <a href="/coloring-pages" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-hover rounded-lg transition-colors duration-200">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              View All Coloring Pages
            </a>
          </div>
        )}

        {/* 显示筛选后的页面 */}
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredPages.map((page: ColoringPage) => (
            <ColoringCard
              id={page.id}
              slug={page.slug}
              title={page.title}
              assetFolder={page.assetFolder}
              categoryInfo={page.categoryInfo}
              tags={page.tags}
              pdfUrl={getImageUrl(page.assetFolder, "monochrome.pdf")}
              pngUrl={getImageUrl(page.assetFolder, "monochrome.png")}
            />
          ))}
        </div>
      </div>

    <style>
      .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
      }

      .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #ddd;
        border-radius: 10px;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #ccc;
      }
    </style>
  </div>
</Layout>

<script>
  // 导入Flowbite的初始化函数
  import { initFlowbite } from 'flowbite';

  // 在页面加载完成后初始化
  document.addEventListener('DOMContentLoaded', () => {
    // 确保Flowbite已初始化
    initFlowbite();

    // 清除筛选按钮
    const clearFiltersBtn = document.getElementById('clear-filters-btn');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', () => {
        window.location.href = '/coloring-pages';
      });
    }

    // 标签展开/收起功能
    const toggleTagsBtn = document.getElementById('toggle-tags-btn');
    const visibleTags = document.getElementById('visible-tags');
    const toggleText = document.getElementById('toggle-text');
    const toggleIcon = document.getElementById('toggle-icon');
    const tagsGradient = document.getElementById('tags-gradient');

    let isExpanded = false;

    if (toggleTagsBtn && visibleTags && toggleText && toggleIcon && tagsGradient) {
      toggleTagsBtn.addEventListener('click', () => {
        isExpanded = !isExpanded;

        if (isExpanded) {
          // 展开标签
          visibleTags.style.maxHeight = `${visibleTags.scrollHeight}px`;
          toggleText.textContent = 'Show Less Tags';
          toggleIcon.style.transform = 'rotate(180deg)';
          tagsGradient.style.opacity = '0';
        } else {
          // 收起标签
          visibleTags.style.maxHeight = '5.5rem';
          toggleText.textContent = 'Show More Tags';
          toggleIcon.style.transform = 'rotate(0)';
          tagsGradient.style.opacity = '1';
        }
      });

      // 检查是否需要显示"显示更多"按钮
      // 如果标签内容高度小于或等于最大高度，则隐藏按钮和渐变
      if (visibleTags.scrollHeight <= parseFloat(visibleTags.style.maxHeight)) {
        toggleTagsBtn.parentElement.style.display = 'none';
        tagsGradient.style.display = 'none';
      }
    }
  });
</script>
