---
import Layout from '@/layouts/Layout.astro';
import CloudflareDownloadButton from '@/components/CloudflareDownloadButton.astro';
import ColoringCard from '@/components/ColoringCard.astro';
import DetailImage from '@/components/DetailImage.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getColoringPageById, getRelatedColoringPagesByTags } from '@/data/coloringPages';
import { getCollection, type CollectionEntry } from 'astro:content';
import type { ColoringPage } from '@/types/coloringPage';

// 启用预渲染
export const prerender = true;

// 定义静态路径
export async function getStaticPaths() {
  const coloringPagesEntries = await getCollection('coloring-pages');

  return coloringPagesEntries.map((entry: CollectionEntry<'coloring-pages'>) => {
    // 使用 slug（文件名）作为路由参数，使用正则表达式确保只替换文件扩展名
    // 同时支持 .md 和 .mdx 文件
    const slug = entry.id.replace(/\.(md|mdx)$/, '');
    return {
      params: { id: slug },
      props: { entry, slug },
    };
  });
}

// 获取URL参数和props
const { id } = Astro.params;
const { entry } = Astro.props;

// 获取页面数据
const coloringPage = await getColoringPageById(id) as ColoringPage;

// 获取 Markdown 内容
let Content: any;
if (entry) {
  const rendered = await entry.render();
  Content = rendered.Content;
}

// 根据当前页面的标签获取相关页面
const relatedPages = await getRelatedColoringPagesByTags(coloringPage?.tags || [], coloringPage?.slug || '', 4);

// 不再需要预先获取下载链接，CloudflareDownloadButton组件会自动处理

// 不再需要为相关页面预先获取下载链接，CloudflareColoringCard组件会自动处理
const relatedPagesWithUrls = relatedPages;

// DetailImage 组件会自动处理图片逻辑
// 如果文件不存在，DetailImage 会显示占位符
---

<Layout
  title={`${coloringPage.title} Coloring Page - PrintableColoringHub`}
  description={`Download free printable ${coloringPage.title} coloring page in PDF or WebP format. Perfect for kids and adults.`}
>
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "All Coloring Pages", href: "/coloring-pages" },
          { label: coloringPage.title, isActive: true }
        ]}
      />
    </div>


    <div class="grid md:grid-cols-2 gap-8 mb-12">
      <div class="space-y-6">
        <div>
          <h3 class="text-xl font-semibold mb-3">Coloring Page</h3>
          <DetailImage
            assetFolder={coloringPage.assetFolder}
            filename="monochrome.png"
            alt={`${coloringPage.title} coloring page`}
            width={800}
            height={800}
            loading="eager"
            class="w-full rounded-lg shadow-lg"
          />
        </div>

        {/* 彩色参考图 */}
        <div>
          <h3 class="text-xl font-semibold mb-3">Color Reference</h3>
          <DetailImage
            assetFolder={coloringPage.assetFolder}
            filename="colored.png"
            alt={`${coloringPage.title} colored reference`}
            width={800}
            height={800}
            loading="lazy"
            class="w-full rounded-lg shadow-lg"
          />
        </div>


      </div>

      <div>
        <h1 class="mb-4">{coloringPage.title}</h1>

        <div class="mb-6">
          <a
            href={`/${coloringPage.categoryInfo.main.toLowerCase().replace(/\s+/g, '-')}-coloring-pages`}
            class="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 border shadow-sm bg-blue-100 text-blue-700 hover:bg-blue-200 border-blue-200 capitalize mr-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
            {coloringPage.categoryInfo.main}
          </a>
          {coloringPage.categoryInfo.sub && (
            <a
              href={`/${coloringPage.categoryInfo.main.toLowerCase().replace(/\s+/g, '-')}-${coloringPage.categoryInfo.sub.toLowerCase().replace(/\s+/g, '-')}`}
              class="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 border shadow-sm bg-indigo-100 text-indigo-700 hover:bg-indigo-200 border-indigo-200 capitalize mr-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              {coloringPage.categoryInfo.sub}
            </a>
          )}
        </div>

        {coloringPage.tags && coloringPage.tags.length > 0 && (
          <div class="mb-6">
            <h3 class="text-lg font-semibold mb-2">Tags</h3>
            <div class="flex flex-wrap gap-2">
              {coloringPage.tags.map((tag: string, index: number) => {
                // 根据索引轮换标签样式
                const colors = [
                  "bg-purple-100 text-purple-700 hover:bg-purple-200 border-purple-200",
                  "bg-blue-100 text-blue-700 hover:bg-blue-200 border-blue-200",
                  "bg-green-100 text-green-700 hover:bg-green-200 border-green-200",
                  "bg-amber-100 text-amber-700 hover:bg-amber-200 border-amber-200"
                ];
                const colorClass = colors[index % colors.length];

                return (
                  <a
                    href={`/tags/${tag.toLowerCase().replace(/\s+/g, '-')}`}
                    class={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 border shadow-sm ${colorClass}`}
                  >
                    <span>#{tag}</span>
                  </a>
                );
              })}
            </div>
          </div>
        )}

        {Content && (
          <div class="prose prose-lg max-w-none mb-8">
            <Content />
          </div>
        )}

        <div class="space-y-8">  {/* 增大标题和按钮间距 */}
          <h3 class="text-3xl font-bold text-primary">Download Options</h3>  {/* 标题更大更突出 */}

          <div class="flex flex-col sm:flex-row gap-8">  {/* 按钮间距更大 */}
            {/* 使用相同的 Tailwind 类，增加高度 */}
            <CloudflareDownloadButton
              className="px-12 py-12 text-2xl font-bold shadow-lg hover:shadow-xl transition-all transform hover:scale-105 flex-1 h-24"
              assetFolder={coloringPage.assetFolder}
              format="PDF"
              fileName={`${coloringPage.title.toLowerCase().replace(/\s+/g, '-')}.pdf`}
            />
            <CloudflareDownloadButton
              className="px-12 py-12 text-2xl font-bold shadow-lg hover:shadow-xl transition-all transform hover:scale-105 flex-1 h-24"
              assetFolder={coloringPage.assetFolder}
              format="PNG"
              fileName={`${coloringPage.title.toLowerCase().replace(/\s+/g, '-')}.png`}
            />
          </div>
        </div>



          <div class="mt-6 pt-6 border-t border-gray-200">
            <h3 class="text-xl font-semibold mb-4">Printing Instructions</h3>
            <ul class="list-disc pl-5 space-y-2 text-gray-700">
              <li>Download your preferred format (PDF recommended for printing)</li>
              <li>Open the PDF file with a PDF reader like Adobe Reader</li>
              <li>For PNG images, you can view them in your browser and print from there</li>
              <li>Select "Print" and choose "Fit to Page" for best results</li>
              <li>Use standard letter size paper (8.5" x 11")</li>
              <li>For best quality, select "High Quality" in your printer settings</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Related Coloring Pages -->
    <div class="mb-12">
      <h2 class="text-2xl font-bold mb-6">You Might Also Like</h2>

      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {relatedPagesWithUrls.map((page) => (
          <ColoringCard
            id={page.id}
            slug={page.slug}
            title={page.title}
            assetFolder={page.assetFolder}
            categoryInfo={page.categoryInfo}
            tags={page.tags}
            pdfUrl={page.pdfUrl}
            pngUrl={page.pngUrl}
          />
        ))}
      </div>
    </div>
  </div>
</Layout>
