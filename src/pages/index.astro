---
import Layout from '../layouts/Layout.astro';
import ColoringCard from '../components/ColoringCard.astro';
import Hero from '../components/Hero.astro';
import { getNewColoringPages, getPopularColoringPages, getCategoriesWithSubcategories } from '../data/coloringPages';
import type { ColoringPage } from '../types/coloringPage';

// 获取热门页面
const popularPages = await getPopularColoringPages();

// 不再需要预先获取下载链接，CloudflareColoringCard组件会自动处理
const popularPagesWithUrls = popularPages;

const categories = await getCategoriesWithSubcategories();

// 获取新页面
const newPages = await getNewColoringPages(4);

// 不再需要预先获取下载链接，CloudflareColoringCard组件会自动处理
const newPagesWithUrls = newPages;
---

<Layout title="PrintableColoringHub - Free Printable Coloring Pages">
  <!-- Hero Section -->
  <Hero />

  <!-- Popular Coloring Pages -->
  <section class="section-content section-light">
    <div class="container-custom">
      <div class="mb-12">
        <h2 class="section-title">Popular Coloring Pages</h2>
        <p class="section-subtitle">
          Browse our most popular coloring pages. These are the favorites among our users and perfect for all ages.
        </p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {popularPagesWithUrls.map((page) => (
          <ColoringCard
            id={page.id}
            slug={page.slug}
            title={page.title}
            assetFolder={page.assetFolder}
            categoryInfo={page.categoryInfo}
            tags={page.tags}
            isFree={!page.premium}
          />
        ))}
      </div>

      <div class="mt-8 text-center">
        <a href="/popular" class="btn btn-primary">
          View All Popular Pages
        </a>
      </div>
    </div>
  </section>

  <!-- Categories Section -->
  <section class="section-content section-dark">
    <div class="container-custom">
      <div class="mb-12">
        <h2 class="section-title">Browse by Category</h2>
        <p class="section-subtitle">
          Find the perfect coloring pages by browsing our categories. We have something for everyone, from animals to superheroes.
        </p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {categories.slice(0, 6).map((category) => (
          <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100">
            <div class="p-5">
              <h3 class="font-semibold mb-1 capitalize">{category.name}</h3>
              <p class="text-sm text-gray-600 mb-3">{category.count} pages</p>
              <a
                href={`/${category.slug}`}
                class="inline-flex items-center text-primary hover:text-primary-hover text-sm"
              >
                <span>Browse all</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>

            {category.subcategories.length > 0 && (
              <div class="px-5 pb-5 pt-0">
                <div class="h-px bg-gray-100 w-full mb-3"></div>
                <div class="flex flex-wrap gap-1">
                  {category.subcategories.slice(0, 3).map((sub) => (
                    <a
                      href={`/${sub.slug}`}
                      class="inline-flex items-center px-2 py-1 bg-gray-100 hover:bg-primary/10 rounded-full text-xs transition-colors"
                    >
                      <span class="capitalize">{sub.name}</span>
                      <span class="ml-1 bg-gray-200 text-gray-700 rounded-full w-4 h-4 inline-flex items-center justify-center text-[10px]">{sub.count}</span>
                    </a>
                  ))}
                  {category.subcategories.length > 3 && (
                    <a
                      href={`/${category.slug}`}
                      class="inline-flex items-center px-2 py-1 bg-gray-100 hover:bg-primary/10 rounded-full text-xs transition-colors"
                    >
                      <span>+{category.subcategories.length - 3} more</span>
                    </a>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      <div class="mt-8 text-center">
        <a href="/categories" class="btn btn-primary">
          View All Categories
        </a>
      </div>
    </div>
  </section>

  <!-- Why Choose Us -->
  <section class="section-content section-light">
    <div class="container-custom">
      <div class="mb-12 text-center">
        <h2 class="section-title">Why Choose Our Coloring Pages</h2>
        <p class="section-subtitle mx-auto">
          Our coloring pages are designed with quality and usability in mind. Here's why you'll love them.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8">
        <div class="feature-box text-center">
          <div class="feature-icon mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 class="text-lg font-semibold mb-3">High Quality</h3>
          <p class="text-gray-600 text-sm leading-relaxed">
            All our coloring pages are high-resolution and print beautifully on standard paper.
          </p>
        </div>

        <div class="feature-box text-center">
          <div class="feature-icon mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-lg font-semibold mb-3">Instant Download</h3>
          <p class="text-gray-600 text-sm leading-relaxed">
            Get immediate access to all coloring pages. No waiting, no email required.
          </p>
        </div>

        <div class="feature-box text-center">
          <div class="feature-icon mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-lg font-semibold mb-3">100% Free</h3>
          <p class="text-gray-600 text-sm leading-relaxed">
            All coloring pages are completely free to download and use.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Recent Additions -->
  <section class="section-content section-dark">
    <div class="container-custom">
      <div class="mb-12">
        <h2 class="section-title">Recent Additions</h2>
        <p class="section-subtitle">
          Check out our newest coloring pages. We regularly add new content to keep your coloring experience fresh and exciting.
        </p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {newPagesWithUrls.map((page) => (
          <ColoringCard
            id={page.id}
            slug={page.slug}
            title={page.title}
            assetFolder={page.assetFolder}
            categoryInfo={page.categoryInfo}
            tags={page.tags}
            isFree={!page.premium}
          />
        ))}
      </div>

      <div class="mt-8 text-center">
        <a href="/new" class="btn btn-primary">
          View All New Pages
        </a>
      </div>
    </div>
  </section>

  <!-- Call to Action -->
  <section class="py-16 bg-gradient-to-b from-white to-gray-50">
    <div class="container-custom text-center">
      <h2 class="text-2xl md:text-3xl font-bold mb-6">Start Coloring Today!</h2>
      <p class="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
        Browse our extensive collection of free printable coloring pages and find your perfect design.
      </p>
      <div class="flex flex-col sm:flex-row justify-center gap-4">
        <a href="/categories" class="btn btn-primary btn-lg">Browse Categories</a>
        <a href="/popular" class="btn btn-outline btn-lg">Popular Pages</a>
        <a href="/coloring-pages" class="btn btn-accent btn-lg">View All Pages</a>
      </div>
    </div>
  </section>
</Layout>
