---
import Layout from '@/layouts/Layout.astro';
import LazyColoringCard from '@/components/LazyColoringCard.astro';
import IntersectionObserver from '@/components/IntersectionObserver.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getNewColoringPages } from '@/data/coloringPages';
import type { ColoringPage } from '@/types/coloringPage';

// 获取最新添加的着色页面（30个）
const newPages = await getNewColoringPages(28);

// 不再需要预先获取下载链接，CloudflareColoringCard组件会自动处理
const newPagesWithUrls = newPages;
---

<Layout title="New Coloring Pages - PrintableColoringHub" description="Browse our newest coloring pages. Fresh content added regularly for kids and adults.">
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "New Coloring Pages", isActive: true }
        ]}
      />
    </div>

    <h1 class="mb-8">New Coloring Pages</h1>

    <p class="text-lg text-gray-700 mb-8">
      Check out our newest coloring pages. We regularly add fresh content to keep your coloring experience exciting.
    </p>

    <IntersectionObserver threshold={0.1} rootMargin="100px">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {newPagesWithUrls.map((page, index) => (
          <LazyColoringCard
            id={page.id}
            slug={page.slug}
            title={page.title}
            assetFolder={page.assetFolder}
            categoryInfo={page.categoryInfo}
            tags={page.tags}
            isFree={!page.premium}
            priority={index < 4}
            index={index}
          />
        ))}
      </div>
    </IntersectionObserver>
  </div>
</Layout>
