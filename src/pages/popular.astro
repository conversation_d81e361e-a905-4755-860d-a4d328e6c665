---
import Layout from '@/layouts/Layout.astro';
import LazyColoringCard from '@/components/LazyColoringCard.astro';
import IntersectionObserver from '@/components/IntersectionObserver.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getPopularColoringPages } from '@/data/coloringPages';
import type { ColoringPage } from '@/types/coloringPage';

// 获取热门页面
const popularPages = await getPopularColoringPages();

// 不再需要预先获取下载链接，CloudflareColoringCard组件会自动处理
const popularPagesWithUrls = popularPages;
---

<Layout title="Popular Coloring Pages - PrintableColoringHub" description="Browse our most popular printable coloring pages. Download free PDF and PNG formats.">
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Popular Coloring Pages", isActive: true }
        ]}
      />
    </div>

    <h1 class="mb-8">Popular Coloring Pages</h1>

    <IntersectionObserver threshold={0.1} rootMargin="100px">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {popularPagesWithUrls.map((page, index) => (
          <LazyColoringCard
            id={page.id}
            slug={page.slug}
            title={page.title}
            assetFolder={page.assetFolder}
            categoryInfo={page.categoryInfo}
            tags={page.tags}
            isFree={!page.premium}
            priority={index < 4}
            index={index}
          />
        ))}
      </div>
    </IntersectionObserver>
  </div>
</Layout>
