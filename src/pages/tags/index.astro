---
import Layout from '@/layouts/Layout.astro';
import { getTagsWithCounts } from '@/data/coloringPages';
import Breadcrumb from '@/components/Breadcrumb.astro';

// 获取所有标签
const tags = await getTagsWithCounts();

// 按计数降序排序
const sortedTags = [...tags].sort((a, b) => b.count - a.count);

// 只显示有多个内容的标签
const filteredTags = sortedTags.filter(tag => tag.count > 1);

// 计算标签总数
const totalTags = filteredTags.length;
---

<Layout
  title="All Tags - PrintableColoringHub"
  description="Browse all tags on PrintableColoringHub. Find coloring pages by topic, theme, or style."
>
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Tags", isActive: true }
        ]}
      />
    </div>

    <h1 class="mb-4">All Tags</h1>

    <p class="text-lg text-gray-700 mb-8">
      Browse our collection of {totalTags} tags. Click on any tag to see all related coloring pages.
    </p>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <div class="flex flex-wrap gap-3">
        {filteredTags.map((tag, index) => {
          // 根据索引轮换标签样式
          const colors = [
            "bg-purple-100 text-purple-700 hover:bg-purple-200 border-purple-200",
            "bg-blue-100 text-blue-700 hover:bg-blue-200 border-blue-200",
            "bg-green-100 text-green-700 hover:bg-green-200 border-green-200",
            "bg-amber-100 text-amber-700 hover:bg-amber-200 border-amber-200"
          ];
          const colorClass = colors[index % colors.length];

          return (
            <a
              href={`/tags/${tag.slug}`}
              class={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 border shadow-sm ${colorClass}`}
            >
              <span>#{tag.name}</span>
              <span class={`ml-2 bg-white rounded-full w-6 h-6 inline-flex items-center justify-center text-xs shadow-sm border ${colorClass.includes('purple') ? 'border-purple-200' : colorClass.includes('blue') ? 'border-blue-200' : colorClass.includes('green') ? 'border-green-200' : 'border-amber-200'}`}>{tag.count}</span>
            </a>
          );
        })}
      </div>
    </div>
  </div>
</Layout>
