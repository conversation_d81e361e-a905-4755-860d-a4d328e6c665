---
import Layout from '@/layouts/Layout.astro';
import CloudflareOptimizedImage from '@/components/CloudflareOptimizedImage.astro';

// 测试的资源文件夹
const testAssetFolders = [
  'grogu_in_pram_best',
  'grogu_sipping_cup',
  'grogu_standing_best',
  'Pikachu-Medieval-Adventure',
  'Ash-Pikachu-Pokemon-Adventure'
];

// 文件类型
const fileTypes = [
  'monochrome.png',
  'colored.png'
];
---

<Layout title="测试图片加载">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">测试图片加载</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {testAssetFolders.map(assetFolder => (
        <div class="border rounded-lg p-4 bg-white shadow-sm">
          <h2 class="text-xl font-semibold mb-4">{assetFolder}</h2>
          
          {fileTypes.map(fileType => (
            <div class="mb-6">
              <h3 class="text-lg font-medium mb-2">{fileType}</h3>
              <div class="aspect-square overflow-hidden rounded-lg border">
                <CloudflareOptimizedImage
                  assetFolder={assetFolder}
                  filename={fileType}
                  alt={`${assetFolder} ${fileType}`}
                  width={300}
                  height={300}
                  loading="eager"
                  class="w-full h-full object-contain"
                  preset="card"
                  format="auto"
                  fit="contain"
                  quality={80}
                  showPlaceholder={true}
                  dpr={2}
                />
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  </div>
</Layout>
