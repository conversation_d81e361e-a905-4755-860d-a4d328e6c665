# MDX 文件格式检查和修复工具

这个目录包含了用于检查和修复 MDX 文件格式的工具脚本。这些脚本可以帮助确保所有 MDX 文件符合项目规范，包括必要的 frontmatter 字段和内容结构。

## 可用脚本

### 1. MDX 验证器 (`mdx-validator.js`)

一个简单的工具，用于验证 MDX 文件是否符合项目规范，并可以自动修复常见问题。

**使用方法:**

```bash
# 验证所有 MDX 文件
yarn check-mdx

# 验证并自动修复所有 MDX 文件
yarn fix-mdx

# 验证单个文件
node src/scripts/mdx-validator.js filename.mdx

# 验证并自动修复单个文件
node src/scripts/mdx-validator.js filename.mdx --fix
```

**验证规则:**

- 必需的 frontmatter 字段 (title, id, assetFolder, categoryInfo, description)
- categoryInfo 结构 (必须包含 main 字段，sub 和 subsub 字段不能为空)
- 数组字段格式 (tags, collections 必须是数组)
- tags 内容格式 (tags 中不能包含纯数字)
- 布尔字段 (featured, premium, popular)
- 日期字段 (dateAdded)
- 数字列表格式 (使用 1. 格式)

### 2. MDX 格式化工具 (`check-and-fix-mdx.js`)

一个更全面的工具，用于检查和修复 MDX 文件格式，基于参考文件的格式。

**使用方法:**

```bash
# 检查所有文件
node src/scripts/check-and-fix-mdx.js check

# 修复所有文件
yarn format-mdx
# 或
node src/scripts/check-and-fix-mdx.js fix

# 检查单个文件
node src/scripts/check-and-fix-mdx.js check filename.mdx

# 修复单个文件
node src/scripts/check-and-fix-mdx.js fix filename.mdx

# 使用自定义参考文件
node src/scripts/check-and-fix-mdx.js fix --reference=custom-reference.mdx
```

**功能:**

- 使用参考文件作为格式标准
- 在修复前创建备份文件
- 检查和修复 frontmatter 字段
- 检查和修复内容结构
- 提供详细的报告

## 参考文件

默认参考文件是 `src/content/coloring-pages/puppy-and-bears-sunny-mowing-day-coloring-page.mdx`。这个文件被认为是格式正确的示例，其他文件将根据这个文件的格式进行检查和修复。

## 备份

在修复文件之前，脚本会自动创建备份文件，保存在 `src/scripts/backups/coloring-pages` 目录中。备份文件名包含时间戳，以便于识别。

## 注意事项

1. 在运行修复脚本之前，建议先运行检查脚本，了解哪些文件需要修复。
2. 修复脚本会尝试保留原始内容，只修复格式问题。
3. 对于复杂的内容结构问题，可能需要手动修复。
4. 如果你不确定脚本的修复结果，可以查看备份文件，比较修复前后的差异。
