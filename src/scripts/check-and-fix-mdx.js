/**
 * MDX 文件检查和修复脚本
 *
 * 此脚本用于检查 MDX 文件是否符合规范，并可以自动修复不符合规范的文件
 *
 * 使用方法:
 * 1. 检查所有文件: node src/scripts/check-and-fix-mdx.js check
 * 2. 修复所有文件: node src/scripts/check-and-fix-mdx.js fix
 * 3. 检查单个文件: node src/scripts/check-and-fix-mdx.js check filename.mdx
 * 4. 修复单个文件: node src/scripts/check-and-fix-mdx.js fix filename.mdx
 * 5. 使用自定义参考文件: node src/scripts/check-and-fix-mdx.js fix --reference=custom-reference.mdx
 */

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

// 默认配置
const CONTENT_DIR = path.join(process.cwd(), 'src/content/coloring-pages');
const DEFAULT_REFERENCE_FILE = 'puppy-and-bears-sunny-mowing-day-coloring-page.mdx';
const BACKUP_DIR = path.join(process.cwd(), 'src/scripts/backups/coloring-pages');

// 必需的 frontmatter 字段
const REQUIRED_FIELDS = [
  'title',
  'id',
  'assetFolder',
  'categoryInfo',
  'description',
  'tags'
];

// 必需的内容部分
const REQUIRED_SECTIONS = [
  { name: 'Creative Coloring Ideas', pattern: /## 🎨 Creative Coloring Ideas/i },
  { name: 'Why Kids Will Love This Page', pattern: /## 💖 Why Kids Will Love This Page|## 💖 Why You'll Love This Page/i }
];

// 获取参考文件路径
function getReferenceFilePath(customReference = null) {
  if (customReference) {
    // 如果提供了自定义参考文件
    if (customReference.includes('/')) {
      // 如果是相对路径
      return path.resolve(process.cwd(), customReference);
    } else {
      // 如果只是文件名
      return path.join(CONTENT_DIR, customReference);
    }
  }
  // 使用默认参考文件
  return path.join(CONTENT_DIR, DEFAULT_REFERENCE_FILE);
}

// 全局变量，可以通过命令行参数修改
let REFERENCE_FILE = getReferenceFilePath();

// 创建备份目录
function createBackupDir() {
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
    console.log(`创建备份目录: ${BACKUP_DIR}`);
  }
}

// 备份文件
function backupFile(filePath) {
  const fileName = path.basename(filePath);
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFileName = `${fileName.replace('.mdx', '')}-${timestamp}.mdx`;
  const backupPath = path.join(BACKUP_DIR, backupFileName);

  fs.copyFileSync(filePath, backupPath);
  return backupPath;
}

// 读取参考文件内容
function readReferenceFile() {
  try {
    const content = fs.readFileSync(REFERENCE_FILE, 'utf8');
    return content;
  } catch (error) {
    console.error(`读取参考文件时出错: ${error.message}`);
    process.exit(1);
  }
}

// 获取所有 MDX 文件
function getAllMdxFiles() {
  const files = fs.readdirSync(CONTENT_DIR);
  return files.filter(file => file.endsWith('.mdx') || file.endsWith('.md'));
}

// 检查 MDX 文件
function checkMdxFile(content) {
  const issues = [];

  // 使用 gray-matter 解析 frontmatter 和内容
  const parsed = matter(content);
  const frontmatter = parsed.data;
  const mdxContent = parsed.content;

  // 检查必需的 frontmatter 字段
  REQUIRED_FIELDS.forEach(field => {
    if (!frontmatter[field]) {
      issues.push(`缺少必需的 frontmatter 字段: ${field}`);
    }
  });

  // 检查 categoryInfo 结构
  if (frontmatter.categoryInfo) {
    if (!frontmatter.categoryInfo.main) {
      issues.push('categoryInfo 缺少 main 字段');
    }

    // 检查是否有空的 sub 或 subsub 字段
    if (Object.prototype.hasOwnProperty.call(frontmatter.categoryInfo, 'sub') &&
        (frontmatter.categoryInfo.sub === '' || frontmatter.categoryInfo.sub === null)) {
      issues.push('categoryInfo 中的 sub 字段为空');
    }

    if (Object.prototype.hasOwnProperty.call(frontmatter.categoryInfo, 'subsub') &&
        (frontmatter.categoryInfo.subsub === '' || frontmatter.categoryInfo.subsub === null)) {
      issues.push('categoryInfo 中的 subsub 字段为空');
    }
  }

  // 检查 tags 是否为数组
  if (frontmatter.tags && !Array.isArray(frontmatter.tags)) {
    issues.push('tags 字段不是数组');
  }

  // 检查 tags 中是否有纯数字
  if (frontmatter.tags && Array.isArray(frontmatter.tags)) {
    const numericTags = frontmatter.tags.filter(tag => /^\d+$/.test(tag));
    if (numericTags.length > 0) {
      issues.push(`tags 中不能包含纯数字: ${numericTags.join(', ')}`);
    }
  }

  // 检查 collections 是否为数组
  if (frontmatter.collections && !Array.isArray(frontmatter.collections)) {
    issues.push('collections 字段不是数组');
  }

  // 检查必需的内容部分
  REQUIRED_SECTIONS.forEach(section => {
    if (!section.pattern.test(mdxContent)) {
      issues.push(`缺少必需的内容部分: ${section.name}`);
    }
  });

  // 检查是否有结尾的渐变背景 div
  const hasClosingDiv = /className="text-center my-8 p-4 bg-gradient-to-r from-\[#[A-F0-9]+\] to-\[#[A-F0-9]+\] rounded-lg"/.test(mdxContent);
  if (!hasClosingDiv) {
    issues.push('缺少结尾的渐变背景 div');
  }

  // 检查数字列表格式
  const hasIncorrectNumbering = /^\d+\.\s+/gm.test(mdxContent) && !/^1\.\s+/gm.test(mdxContent);
  if (hasIncorrectNumbering) {
    issues.push('数字列表格式不正确，应使用 1. 格式');
  }

  // 检查 dateAdded 字段是否带引号
  if (frontmatter.dateAdded) {
    // 检查 dateAdded 是否为字符串类型且带有引号
    const isString = typeof frontmatter.dateAdded === 'string';
    // 如果是字符串，检查是否是被引号包裹的日期字符串（在YAML中表现为带引号的日期）
    const hasQuotes = isString && /^\s*['"].*['"]\s*$/.test(JSON.stringify(frontmatter.dateAdded));

    if (hasQuotes) {
      issues.push('dateAdded 字段不应该带引号');
    }
  }

  return issues;
}

// 修复 MDX 内容
function fixMdxContent(content) {
  // 使用 gray-matter 解析 frontmatter 和内容
  const parsed = matter(content);

  // 保留原始 frontmatter 数据
  const frontmatter = parsed.data;
  let mdxContent = parsed.content;

  // 确保 frontmatter 中有 categoryInfo 字段（必需字段）
  if (!frontmatter.categoryInfo) {
    // 如果有旧的 category 字段，尝试转换
    if (frontmatter.category) {
      frontmatter.categoryInfo = parseCategoryString(frontmatter.category);
      // 移除旧的 category 字段
      delete frontmatter.category;
    } else {
      // 添加默认分类
      frontmatter.categoryInfo = { main: 'Uncategorized' };
    }
  }

  // 处理 categoryInfo 中的空字段
  if (frontmatter.categoryInfo) {
    // 移除空的 sub 字段
    if (frontmatter.categoryInfo.sub === '' || frontmatter.categoryInfo.sub === null || frontmatter.categoryInfo.sub === undefined) {
      delete frontmatter.categoryInfo.sub;
    }

    // 移除空的 subsub 字段
    if (frontmatter.categoryInfo.subsub === '' || frontmatter.categoryInfo.subsub === null || frontmatter.categoryInfo.subsub === undefined) {
      delete frontmatter.categoryInfo.subsub;
    }
  }

  // 确保有 collections 字段
  if (!frontmatter.collections) {
    frontmatter.collections = [];
  }

  // 确保有 featured 字段
  if (frontmatter.featured === undefined) {
    frontmatter.featured = false;
  }

  // 确保有 premium 字段
  if (frontmatter.premium === undefined) {
    frontmatter.premium = false;
  }

  // 确保有 popular 字段
  if (frontmatter.popular === undefined) {
    frontmatter.popular = false;
  }

  // 确保有 dateAdded 字段
  if (!frontmatter.dateAdded) {
    frontmatter.dateAdded = new Date().toISOString();
  } else if (typeof frontmatter.dateAdded === 'string') {
    // 处理 dateAdded 字段的引号问题
    try {
      // 尝试将字符串转换为日期对象
      const dateStr = frontmatter.dateAdded.replace(/^['"]|['"]$/g, '');
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        // 如果是有效日期，使用ISO字符串格式
        frontmatter.dateAdded = date.toISOString();
      }
    } catch (e) {
      // 如果转换失败，使用当前日期
      frontmatter.dateAdded = new Date().toISOString();
    }
  }

  // 确保有 tags 字段
  if (!frontmatter.tags) {
    frontmatter.tags = [];
  }

  // 确保 tags 是数组
  if (!Array.isArray(frontmatter.tags)) {
    frontmatter.tags = [frontmatter.tags];
  }

  // 移除 tags 中的纯数字
  if (Array.isArray(frontmatter.tags)) {
    frontmatter.tags = frontmatter.tags.filter(tag => !/^\d+$/.test(tag));

    // 如果过滤后 tags 为空，添加一个默认标签
    if (frontmatter.tags.length === 0 && frontmatter.title) {
      const defaultTag = frontmatter.title.toLowerCase().replace(/[^a-z0-9\s]/g, '').split(' ')[0];
      frontmatter.tags = [defaultTag];
    }
  }

  // 确保 collections 是数组
  if (!Array.isArray(frontmatter.collections)) {
    frontmatter.collections = [frontmatter.collections];
  }

  // 格式化数字列表 - 确保使用 1. 格式
  mdxContent = mdxContent.replace(/^\d+\.\s+/gm, '1. ');

  // 确保有创意着色建议部分
  if (!mdxContent.includes('## 🎨 Creative Coloring Ideas')) {
    // 在文件末尾添加
    mdxContent += `\n\n## 🎨 Creative Coloring Ideas

### Color Scheme Suggestions:
- Try bright, vibrant colors for a cheerful look
- Use soft pastels for a gentle, calming effect
- Experiment with rainbow patterns for extra fun

### Fun Techniques:
- Add patterns or textures to larger areas
- Try shading with colored pencils for depth
- Use metallic or glitter pens for special details`;
  }

  // 确保有标准的部分标题
  if (!mdxContent.includes('## 💖 Why Kids Will Love This Page') &&
      !mdxContent.includes('## 💖 Why You\'ll Love This Page')) {
    // 查找可能的插入点
    const insertPoint = mdxContent.indexOf('## 🎨');
    if (insertPoint !== -1) {
      const whyLoveSection = `\n\n## 💖 Why Kids Will Love This Page\n
- Encourages creativity and imagination
- Develops fine motor skills and color recognition
- Perfect for a quiet afternoon activity
- Creates a sense of accomplishment when completed\n\n`;

      mdxContent = mdxContent.substring(0, insertPoint) + whyLoveSection + mdxContent.substring(insertPoint);
    } else {
      // 如果找不到插入点，添加到内容末尾
      mdxContent += `\n\n## 💖 Why Kids Will Love This Page\n
- Encourages creativity and imagination
- Develops fine motor skills and color recognition
- Perfect for a quiet afternoon activity
- Creates a sense of accomplishment when completed`;
    }
  }

  // 如果没有结尾的渐变背景 div，添加一个
  if (!mdxContent.includes('className="text-center my-8 p-4 bg-gradient-to-r from-[#')) {
    const closingDiv = `\n\n<div className="text-center my-8 p-4 bg-gradient-to-r from-[#D4FFBD] to-[#A2FFD0] rounded-lg">
  <h3 className="text-[#FF6B6B]">Share Your Masterpiece!</h3>
  <p>We'd love to see how you brought this scene to life! Tag us on social media with <strong>#PrintableColoringHub</strong></p>
</div>`;
    mdxContent = mdxContent + closingDiv;
  }

  // 确保内容以空行开始
  if (!mdxContent.startsWith('\n')) {
    mdxContent = '\n' + mdxContent;
  }

  // 重新组合 frontmatter 和内容
  return matter.stringify(mdxContent, frontmatter);
}

// 解析分类字符串
function parseCategoryString(categoryStr) {
  if (!categoryStr) return { main: 'Uncategorized' };

  const parts = categoryStr.split('>').map(part => part.trim());

  const categoryInfo = {
    main: parts[0] || 'Uncategorized'
  };

  if (parts.length > 1 && parts[1]) {
    categoryInfo.sub = parts[1];
  }

  if (parts.length > 2 && parts[2]) {
    categoryInfo.subsub = parts[2];
  }

  return categoryInfo;
}

// 处理所有文件
function processAllFiles(mode) {
  // 创建备份目录
  if (mode === 'fix') {
    createBackupDir();
  }

  // 读取参考文件
  const referenceContent = readReferenceFile();
  const reference = matter(referenceContent);
  console.log(`成功读取参考文件: ${path.basename(REFERENCE_FILE)}`);
  console.log(`参考文件标题: "${reference.data.title}"`);

  // 获取所有 MDX 文件
  const files = getAllMdxFiles();
  console.log(`找到 ${files.length} 个 MDX 文件`);

  let processed = 0;
  let skipped = 0;
  let issueCount = 0;
  let fixedCount = 0;

  // 显示参考文件信息
  console.log(`使用参考文件: ${path.basename(REFERENCE_FILE)}`);

  // 处理每个文件
  files.forEach(file => {
    const filePath = path.join(CONTENT_DIR, file);

    // 跳过参考文件
    if (filePath === REFERENCE_FILE) {
      console.log(`跳过参考文件: ${file}`);
      skipped++;
      return;
    }

    try {
      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');

      // 检查文件
      const issues = checkMdxFile(content);

      if (issues.length > 0) {
        console.log(`\n文件 ${file} 存在 ${issues.length} 个问题:`);
        issues.forEach(issue => console.log(`- ${issue}`));
        issueCount += issues.length;

        // 如果是修复模式，修复文件
        if (mode === 'fix') {
          // 备份文件
          const backupPath = backupFile(filePath);
          console.log(`已备份: ${file} -> ${path.basename(backupPath)}`);

          // 修复内容
          let fixedContent = fixMdxContent(content);

          // 直接替换文件内容中的带引号的dateAdded字段
          fixedContent = fixedContent.replace(/(dateAdded:\s*)['"]([^'"]+)['"]/g, '$1$2');

          // 写入文件
          fs.writeFileSync(filePath, fixedContent);

          console.log(`已修复: ${file}`);
          fixedCount++;
        }
      } else {
        if (mode === 'check') {
          console.log(`✓ 文件 ${file} 格式正确`);
        }
      }

      processed++;
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error);
    }
  });

  // 显示统计信息
  console.log(`\n处理完成!`);
  console.log(`- 总文件数: ${files.length}`);
  console.log(`- 已处理: ${processed}`);
  console.log(`- 已跳过: ${skipped}`);

  if (mode === 'check') {
    console.log(`- 发现问题: ${issueCount}`);
  } else if (mode === 'fix') {
    console.log(`- 已修复文件: ${fixedCount}`);
  }
}

// 处理单个文件
function processSingleFile(fileName, mode) {
  // 创建备份目录
  if (mode === 'fix') {
    createBackupDir();
  }

  // 读取参考文件
  const referenceContent = readReferenceFile();
  const reference = matter(referenceContent);
  console.log(`成功读取参考文件: ${path.basename(REFERENCE_FILE)}`);
  console.log(`参考文件标题: "${reference.data.title}"`);

  // 构建完整文件路径
  let filePath;
  if (fileName.includes('/')) {
    // 如果提供了相对路径
    filePath = path.resolve(process.cwd(), fileName);
  } else {
    // 如果只提供了文件名
    filePath = path.join(CONTENT_DIR, fileName);
  }

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    console.error(`错误: 文件不存在 - ${filePath}`);
    return;
  }

  // 检查是否是 MDX 文件
  if (!filePath.endsWith('.mdx') && !filePath.endsWith('.md')) {
    console.error(`错误: 不是 MDX 文件 - ${filePath}`);
    return;
  }

  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    const parsed = matter(content);
    console.log(`处理文件: "${parsed.data.title}"`);

    // 检查文件
    const issues = checkMdxFile(content);

    if (issues.length > 0) {
      console.log(`\n文件 ${path.basename(filePath)} 存在 ${issues.length} 个问题:`);
      issues.forEach(issue => console.log(`- ${issue}`));

      // 如果是修复模式，修复文件
      if (mode === 'fix') {
        // 备份文件
        const backupPath = backupFile(filePath);
        console.log(`已备份: ${path.basename(filePath)} -> ${path.basename(backupPath)}`);

        // 修复内容
        let fixedContent = fixMdxContent(content);

        // 直接替换文件内容中的带引号的dateAdded字段
        fixedContent = fixedContent.replace(/(dateAdded:\s*)['"]([^'"]+)['"]/g, '$1$2');

        // 写入文件
        fs.writeFileSync(filePath, fixedContent);

        console.log(`已修复: ${path.basename(filePath)}`);
      }
    } else {
      console.log(`✓ 文件 ${path.basename(filePath)} 格式正确`);
    }
  } catch (error) {
    console.error(`处理文件 ${path.basename(filePath)} 时出错:`, error);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log(`
MDX 文件检查和修复脚本

使用方法:
1. 检查所有文件: node src/scripts/check-and-fix-mdx.js check
2. 修复所有文件: node src/scripts/check-and-fix-mdx.js fix
3. 检查单个文件: node src/scripts/check-and-fix-mdx.js check filename.mdx
4. 修复单个文件: node src/scripts/check-and-fix-mdx.js fix filename.mdx
5. 使用自定义参考文件: node src/scripts/check-and-fix-mdx.js fix --reference=custom-reference.mdx
`);
    return;
  }

  // 解析命令行参数
  const mode = args[0]; // 'check' 或 'fix'

  // 检查模式是否有效
  if (mode !== 'check' && mode !== 'fix') {
    console.error(`错误: 无效的模式 "${mode}"，应为 "check" 或 "fix"`);
    return;
  }

  // 检查是否有自定义参考文件
  const referenceArg = args.find(arg => arg.startsWith('--reference='));
  if (referenceArg) {
    const customReference = referenceArg.split('=')[1];
    REFERENCE_FILE = getReferenceFilePath(customReference);
    console.log(`使用自定义参考文件: ${customReference}`);
  }

  // 检查是否指定了文件名
  const fileArg = args.find(arg => !arg.startsWith('--') && arg !== 'check' && arg !== 'fix');

  if (fileArg) {
    // 处理单个文件
    processSingleFile(fileArg, mode);
  } else {
    // 处理所有文件
    processAllFiles(mode);
  }
}

// 执行主函数
main();
