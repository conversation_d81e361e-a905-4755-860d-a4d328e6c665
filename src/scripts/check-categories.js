/**
 * 检查分类结果脚本
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import matter from 'gray-matter';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 内容目录
const CONTENT_DIR = path.join(process.cwd(), 'src/content/coloring-pages');

// 获取所有MDX文件
function getAllMdxFiles() {
  const files = fs.readdirSync(CONTENT_DIR);
  return files.filter(file => file.endsWith('.mdx') || file.endsWith('.md'));
}

// 主函数
function main() {
  console.log('检查着色页面分类...');

  // 获取所有MDX文件
  const files = getAllMdxFiles();
  console.log(`找到 ${files.length} 个MDX文件`);

  const categoryStats = {};
  const mainCategories = {};

  // 处理每个文件
  for (const file of files) {
    const filePath = path.join(CONTENT_DIR, file);

    try {
      // 读取文件内容
      const fileContent = fs.readFileSync(filePath, 'utf8');

      // 解析frontmatter
      const { data } = matter(fileContent);

      // 获取分类信息
      const categoryInfo = data.categoryInfo || { main: 'Uncategorized' };
      const mainCategory = categoryInfo.main;
      const subCategory = categoryInfo.sub;

      // 构建分类字符串用于显示
      const categoryStr = subCategory
        ? `${mainCategory} > ${subCategory}`
        : mainCategory;

      // 更新统计信息
      if (!categoryStats[categoryStr]) {
        categoryStats[categoryStr] = [];
      }
      categoryStats[categoryStr].push(file);

      // 确保主分类存在于映射中
      if (!mainCategories[mainCategory]) {
        mainCategories[mainCategory] = new Set();
      }

      // 如果有子分类，添加到主分类的子分类集合中
      if (subCategory) {
        mainCategories[mainCategory].add(subCategory);
      }
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error);
    }
  }

  console.log('\n分类统计:');

  // 显示分类统计
  for (const [category, files] of Object.entries(categoryStats)) {
    console.log(`- ${category}: ${files.length} 个文件`);
    // 显示前3个文件作为示例
    const examples = files.slice(0, 3);
    console.log(`  示例: ${examples.join(', ')}${files.length > 3 ? '...' : ''}`);
  }

  console.log('\n主分类及其子分类:');

  // 显示主分类及其子分类
  for (const [mainCategory, subCategories] of Object.entries(mainCategories)) {
    console.log(`- ${mainCategory}`);
    if (subCategories.size > 0) {
      const subCategoriesArray = Array.from(subCategories);
      console.log(`  子分类: ${subCategoriesArray.join(', ')}`);
    } else {
      console.log(`  无子分类`);
    }
  }
}

// 执行主函数
main();
