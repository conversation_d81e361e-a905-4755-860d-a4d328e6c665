/**
 * Cloudflare R2资源回退脚本
 *
 * 该脚本用于处理Cloudflare R2资源的加载失败情况，
 * 当资源加载失败时，会尝试加载其他可能的URL。
 */

document.addEventListener('DOMContentLoaded', () => {
  // 处理下载按钮
  setupDownloadButtons();

  // 处理图片加载
  setupImageFallback();
});

/**
 * 设置下载按钮的回退机制
 */
function setupDownloadButtons() {
  const downloadButtons = document.querySelectorAll('[data-multiple-urls]');

  downloadButtons.forEach(button => {
    // 确保按钮有download属性
    if (!button.hasAttribute('download')) {
      const fileType = button.getAttribute('data-file-type');
      button.setAttribute('download', `coloring-page.${fileType || 'pdf'}`);
    }

    button.addEventListener('click', function(e) {
      // 阻止默认行为
      e.preventDefault();

      // 获取所有可能的URL
      const urlsStr = this.getAttribute('data-urls');
      if (!urlsStr) {
        console.error('No data-urls attribute found on download button');
        return;
      }

      const urls = urlsStr.split(',').filter(url => url.trim() !== '');
      if (urls.length === 0) {
        console.error('No valid URLs found in data-urls attribute');
        return;
      }

      const fileName = this.getAttribute('download');
      if (!fileName) {
        console.warn('No download attribute found, using default filename');
      }

      // 尝试下载第一个URL
      tryDownload(urls, 0, fileName || 'download');
    });
  });
}

/**
 * 尝试下载指定URL
 * @param {string[]} urls - 可能的URL数组
 * @param {number} index - 当前尝试的URL索引
 * @param {string} fileName - 下载文件名
 */
function tryDownload(urls, index, fileName) {
  if (index >= urls.length) {
    // 所有URL都尝试过了，但都失败了
    console.error('All download URLs failed');
    alert('无法下载文件，请稍后再试。');
    return;
  }

  const url = urls[index].trim();
  if (!url) {
    // 跳过空URL
    tryDownload(urls, index + 1, fileName);
    return;
  }

  console.log(`Trying to download from URL: ${url}`);

  // 直接尝试下载，不进行预检查
  // 由于CORS限制，HEAD请求可能会失败，但直接下载可能成功
  executeDownload(url, fileName);

  // 注意：如果下载失败，用户需要手动重试
  // 我们无法可靠地检测到下载是否成功，因为浏览器安全限制
}

/**
 * 执行下载操作
 * @param {string} url - 下载URL
 * @param {string} fileName - 文件名
 */
function executeDownload(url, fileName) {
  // 使用代理服务器下载文件
  const proxyUrl = `/api/download?url=${encodeURIComponent(url)}&fileName=${encodeURIComponent(fileName)}`;

  // 创建一个隐藏的a元素
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = proxyUrl;

  // 确保设置了download属性，这是关键
  a.setAttribute('download', fileName);

  // 添加到文档中
  document.body.appendChild(a);

  // 尝试下载
  a.click();

  // 清理
  setTimeout(() => {
    document.body.removeChild(a);
  }, 100);
}

/**
 * 设置图片加载的回退机制
 */
function setupImageFallback() {
  // 查找所有带有data-fallback-urls属性的图片
  const images = document.querySelectorAll('img[data-fallback-urls]');

  images.forEach(img => {
    // 当图片加载失败时
    img.addEventListener('error', function() {
      // 获取所有可能的URL
      const fallbackUrls = this.getAttribute('data-fallback-urls');
      if (!fallbackUrls) {
        console.warn('Image has no fallback URLs');
        showPlaceholder(this);
        return;
      }

      const urls = fallbackUrls.split(',').filter(url => url.trim() !== '');
      if (urls.length === 0) {
        console.warn('No valid fallback URLs found');
        showPlaceholder(this);
        return;
      }

      const currentSrc = this.src;

      // 找到当前URL在数组中的索引
      const currentIndex = urls.indexOf(currentSrc);

      // 如果找到了当前URL，并且还有下一个URL可以尝试
      if (currentIndex !== -1 && currentIndex < urls.length - 1) {
        console.log(`Image load failed for ${currentSrc}, trying next URL: ${urls[currentIndex + 1]}`);
        // 尝试下一个URL
        this.src = urls[currentIndex + 1];
      } else {
        // 如果当前URL不在列表中，或者已经是最后一个URL，则尝试第一个URL
        if (currentIndex === -1 && urls.length > 0 && !this.dataset.triedFirst) {
          console.log(`Image load failed for ${currentSrc}, trying first URL: ${urls[0]}`);
          this.dataset.triedFirst = 'true';
          this.src = urls[0];
        } else {
          // 如果已经尝试了所有URL，但仍然失败，则显示一个占位符图片
          console.log(`All URLs failed for image, showing placeholder`);
          showPlaceholder(this);
        }
      }
    });
  });
}

/**
 * 显示占位符图片
 * @param {HTMLImageElement} img - 图片元素
 */
function showPlaceholder(img) {
  // 使用Cloudflare R2上的占位符图片
  img.src = 'https://static.printablecoloringhub.com/images/placeholder.svg'; // 使用SVG占位符
  img.alt = 'Image not available';
  img.classList.add('image-not-found');

  // 添加占位符样式
  img.style.backgroundColor = '#f0f0f0';
  img.style.objectFit = 'contain';
  img.style.padding = '10px';
}
