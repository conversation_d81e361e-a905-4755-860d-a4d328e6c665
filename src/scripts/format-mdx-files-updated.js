/**
 * 更新版 MDX 文件格式化脚本
 *
 * 此脚本将所有 MDX 文件格式化为与参考文件相同的格式，并确保符合最新的数据结构
 * 参考文件: src/content/coloring-pages/puppy-and-bears-sunny-mowing-day-coloring-page.mdx
 *
 * 使用方法:
 * 1. 确保已安装依赖: npm install gray-matter
 * 2. 运行脚本: node src/scripts/format-mdx-files-updated.js
 * 3. 脚本会在格式化前创建备份文件
 */

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

// 默认配置
const CONTENT_DIR = path.join(process.cwd(), 'src/content/coloring-pages');
const DEFAULT_REFERENCE_FILE = 'puppy-and-bears-sunny-mowing-day-coloring-page.mdx';
const BACKUP_DIR = path.join(process.cwd(), 'src/content/coloring-pages-backup');

// 分类映射 - 扩展版本
const CATEGORY_MAPPING = {
  // 动物分类
  'Animals': {
    keywords: ['dog', 'puppy', 'cat', 'kitten', 'pet', 'animal', 'bear', 'butterfly'],
    subcategories: {
      'Dogs': {
        keywords: ['dog', 'puppy', 'corgi', 'retriever', 'mastiff', 'husky', 'shepherd', 'dachshund', 'basset', 'shiba', 'canine']
      },
      'Cats': {
        keywords: ['cat', 'kitten', 'feline', 'tabby', 'kitty', 'caticorn']
      },
      'Butterflies': {
        keywords: ['butterfly', 'butterflies', 'wing']
      }
    }
  },
  // 家庭分类
  'Family': {
    keywords: ['mom', 'mother', 'daughter', 'family', 'grandma', 'grandmother', 'granddaughter'],
    subcategories: {
      'Mother\'s Day': {
        keywords: ['mother\'s day', 'mom', 'mother']
      },
      'Family Activities': {
        keywords: ['family', 'activity', 'together', 'celebration']
      },
      'Grandparent Time': {
        keywords: ['grandma', 'grandmother', 'granddaughter', 'grandparent']
      }
    }
  },
  // 室内场景
  'Indoor Scenes': {
    keywords: ['bedroom', 'kitchen', 'workshop', 'indoor', 'room', 'home', 'house'],
    subcategories: {
      'Bedroom': {
        keywords: ['bedroom', 'bed', 'sleep', 'night']
      },
      'Kitchen': {
        keywords: ['kitchen', 'cooking', 'baking', 'food']
      },
      'Workshop': {
        keywords: ['workshop', 'craft', 'tools', 'diy']
      }
    }
  },
  // 户外活动
  'Outdoor Activities': {
    keywords: ['garden', 'gardening', 'outdoor', 'hiking', 'travel', 'beach', 'park'],
    subcategories: {
      'Garden': {
        keywords: ['garden', 'gardening', 'plants', 'flowers', 'yard', 'mowing']
      },
      'Travel': {
        keywords: ['travel', 'vacation', 'trip', 'journey', 'airplane', 'suitcase']
      }
    }
  },
  // 节日
  'Holidays': {
    keywords: ['christmas', 'halloween', 'holiday', 'celebration'],
    subcategories: {
      'Christmas': {
        keywords: ['christmas', 'santa', 'gift', 'present', 'tree']
      }
    }
  }
};

// 获取参考文件路径
function getReferenceFilePath(customReference = null) {
  if (customReference) {
    // 如果提供了自定义参考文件
    if (customReference.includes('/')) {
      // 如果是相对路径
      return path.resolve(process.cwd(), customReference);
    } else {
      // 如果只是文件名
      return path.join(CONTENT_DIR, customReference);
    }
  }
  // 使用默认参考文件
  return path.join(CONTENT_DIR, DEFAULT_REFERENCE_FILE);
}

// 全局变量，可以通过命令行参数修改
let REFERENCE_FILE = getReferenceFilePath();

// 读取参考文件内容
function readReferenceFile() {
  try {
    const content = fs.readFileSync(REFERENCE_FILE, 'utf8');
    return content;
  } catch (error) {
    console.error(`读取参考文件时出错: ${error.message}`);
    process.exit(1);
  }
}

// 创建备份目录
function createBackupDir() {
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
    console.log(`创建备份目录: ${BACKUP_DIR}`);
  }
}

// 备份文件
function backupFile(filePath) {
  const fileName = path.basename(filePath);
  const backupPath = path.join(BACKUP_DIR, fileName);
  fs.copyFileSync(filePath, backupPath);
  return backupPath;
}

// 获取所有 MDX 文件
function getAllMdxFiles() {
  const files = fs.readdirSync(CONTENT_DIR);
  return files.filter(file => file.endsWith('.mdx') || file.endsWith('.md'));
}

// 根据标签和内容确定最佳分类
function determineBestCategory(tags, title, description) {
  // 合并所有文本以进行关键词匹配
  const allText = [
    title || '',
    description || '',
    ...(tags || [])
  ].join(' ').toLowerCase();

  // 评分系统
  const categoryScores = {};

  // 遍历所有主分类
  for (const [mainCategory, mainInfo] of Object.entries(CATEGORY_MAPPING)) {
    // 初始化主分类得分
    categoryScores[mainCategory] = {
      score: 0,
      subcategory: null,
      subcategoryScore: 0
    };

    // 检查主分类关键词
    for (const keyword of mainInfo.keywords) {
      if (allText.includes(keyword.toLowerCase())) {
        categoryScores[mainCategory].score += 1;
      }
    }

    // 检查子分类关键词
    for (const [subCategory, subInfo] of Object.entries(mainInfo.subcategories)) {
      let subScore = 0;
      for (const keyword of subInfo.keywords) {
        if (allText.includes(keyword.toLowerCase())) {
          subScore += 1;
        }
      }

      // 如果这个子分类得分更高，更新
      if (subScore > categoryScores[mainCategory].subcategoryScore) {
        categoryScores[mainCategory].subcategory = subCategory;
        categoryScores[mainCategory].subcategoryScore = subScore;
      }
    }
  }

  // 找出得分最高的主分类
  let bestMainCategory = 'General';
  let bestMainScore = 0;

  for (const [category, info] of Object.entries(categoryScores)) {
    if (info.score > bestMainScore) {
      bestMainCategory = category;
      bestMainScore = info.score;
    }
  }

  // 构建结果
  const result = {
    main: bestMainCategory
  };

  // 如果有子分类且得分大于0，添加子分类
  if (categoryScores[bestMainCategory].subcategory && 
      categoryScores[bestMainCategory].subcategoryScore > 0) {
    result.sub = categoryScores[bestMainCategory].subcategory;
  }

  return result;
}

// 格式化 MDX 内容
function formatMdxContent(content) {
  // 使用 gray-matter 解析 frontmatter 和内容
  const parsed = matter(content);

  // 保留原始 frontmatter 数据
  const frontmatter = parsed.data;

  // 确保 frontmatter 中有 categoryInfo 字段（必需字段）
  if (!frontmatter.categoryInfo) {
    // 根据标签、标题和描述推断合适的类别
    const tags = frontmatter.tags || [];
    const title = frontmatter.title || '';
    const description = frontmatter.description || '';

    // 使用增强的分类确定函数
    frontmatter.categoryInfo = determineBestCategory(tags, title, description);

    console.log(`添加缺少的 categoryInfo 字段: "${frontmatter.categoryInfo.main}${frontmatter.categoryInfo.sub ? ' > ' + frontmatter.categoryInfo.sub : ''}"`);
  }

  // 移除旧的 category 字段（如果存在）
  if (frontmatter.category) {
    delete frontmatter.category;
  }

  // 确保有 collections 字段
  if (!frontmatter.collections) {
    frontmatter.collections = [];
  }

  // 确保有 featured 字段
  if (frontmatter.featured === undefined) {
    frontmatter.featured = false;
  }

  // 确保有 premium 字段
  if (frontmatter.premium === undefined) {
    frontmatter.premium = false;
  }

  // 确保有 popular 字段
  if (frontmatter.popular === undefined) {
    frontmatter.popular = false;
  }

  // 确保有 dateAdded 字段
  if (!frontmatter.dateAdded) {
    frontmatter.dateAdded = new Date().toISOString();
  }

  // 获取内容部分
  let mdxContent = parsed.content;

  // 清理内容 - 移除多余的空行
  mdxContent = mdxContent.replace(/\n{3,}/g, '\n\n');

  // 移除水平线
  mdxContent = mdxContent.replace(/^---+$/gm, '');

  // 格式化下载部分 - 移除表格和冗长的描述
  mdxContent = mdxContent.replace(/## ⬇️ Download Your Free Coloring Page[\s\S]*?Choose the format that works best for you:[\s\S]*?(##|$)/g,
    '## ⬇️ Download Your Free Coloring Page\n\n$1');

  // 移除下载格式描述
  mdxContent = mdxContent.replace(/➡️ Download PDF Format \(Ideal for high-quality printing\)/g, '');
  mdxContent = mdxContent.replace(/➡️ Download PNG Format \(Good for digital coloring or resizing\)/g, '');

  // 移除冗长的结尾文本
  mdxContent = mdxContent.replace(/We hope you have a delicious time[\s\S]*?Happy Creating!.*$/g, '');
  mdxContent = mdxContent.replace(/We hope you enjoy coloring[\s\S]*?Happy coloring!.*$/g, '');

  // 将 HTML 标签转换为 Markdown 格式（简单情况）
  mdxContent = mdxContent.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
  mdxContent = mdxContent.replace(/<em>(.*?)<\/em>/g, '*$1*');

  // 格式化标题样式 - 确保使用 ## 和 emoji
  mdxContent = mdxContent.replace(/^# (.*?)$/gm, '## $1');

  // 确保列表项使用 - 而不是 *
  mdxContent = mdxContent.replace(/^\* /gm, '- ');

  // 格式化数字列表 - 确保使用 1. 格式
  mdxContent = mdxContent.replace(/^\d+\.\s+/gm, '1. ');

  // 确保有创意着色建议部分
  if (!mdxContent.includes('## 🎨 Creative Coloring Ideas')) {
    // 在文件末尾添加
    mdxContent += `\n\n## 🎨 Creative Coloring Ideas

### Color Scheme Suggestions:
- Try bright, vibrant colors for a cheerful look
- Use soft pastels for a gentle, calming effect
- Experiment with rainbow patterns for extra fun

### Fun Techniques:
- Add patterns or textures to larger areas
- Try shading with colored pencils for depth
- Use metallic or glitter pens for special details\n\n`;
  }

  // 保留特定的 div 样式（如渐变背景）
  // 确保开头和结尾有一个漂亮的渐变背景 div
  const hasOpeningDiv = mdxContent.includes('<div className="text-center my-8 p-4 bg-gradient-to-r');
  const hasClosingDiv = mdxContent.includes('<div className="text-center my-8 p-4 bg-gradient-to-r', mdxContent.lastIndexOf('<div'));

  // 如果没有开头的渐变背景 div，添加一个
  if (!hasOpeningDiv) {
    const gradientDiv = `<div className="text-center my-8 p-4 bg-gradient-to-r from-[#D4FFBD] to-[#A2FFD0] rounded-lg">
  <h3 className="text-[#3EBD93] mt-0">Coloring Fun Awaits!</h3>
  Grab your crayons and get ready for a creative adventure with this delightful coloring page!
</div>\n\n`;
    mdxContent = gradientDiv + mdxContent;
  }

  // 如果没有结尾的渐变背景 div，添加一个
  if (!hasClosingDiv) {
    const closingDiv = `\n\n<div className="text-center my-8 p-4 bg-gradient-to-r from-[#D4FFBD] to-[#A2FFD0] rounded-lg">
  <h3 className="text-[#FF6B6B]">Share Your Masterpiece!</h3>
  <p>We'd love to see how you brought this scene to life! Tag us on social media with <strong>#PrintableColoringHub</strong></p>
</div>`;
    mdxContent = mdxContent + closingDiv;
  }

  // 确保内容以空行开始
  if (!mdxContent.startsWith('\n')) {
    mdxContent = '\n' + mdxContent;
  }

  // 重新组合 frontmatter 和内容
  return matter.stringify(mdxContent, frontmatter);
}

// 处理所有文件
function processAllFiles() {
  // 创建备份目录
  createBackupDir();

  // 读取参考文件
  const referenceContent = readReferenceFile();
  const reference = matter(referenceContent);
  console.log(`成功读取参考文件: ${path.basename(REFERENCE_FILE)}`);
  console.log(`参考文件标题: "${reference.data.title}"`);

  // 获取所有 MDX 文件
  const files = getAllMdxFiles();
  console.log(`找到 ${files.length} 个 MDX 文件`);

  let processed = 0;
  let skipped = 0;
  let backupCount = 0;

  // 显示参考文件信息
  console.log(`使用参考文件: ${path.basename(REFERENCE_FILE)}`);

  // 处理每个文件
  files.forEach(file => {
    const filePath = path.join(CONTENT_DIR, file);

    // 跳过参考文件
    if (filePath === REFERENCE_FILE) {
      console.log(`跳过参考文件: ${file}`);
      skipped++;
      return;
    }

    try {
      // 备份文件
      const backupPath = backupFile(filePath);
      backupCount++;

      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');

      // 格式化内容
      const formattedContent = formatMdxContent(content);

      // 写入文件
      fs.writeFileSync(filePath, formattedContent);

      console.log(`已处理: ${file} (备份至: ${path.basename(backupPath)})`);
      processed++;
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error);
    }
  });

  console.log(`\n处理完成!`);
  console.log(`- 已处理: ${processed} 个文件`);
  console.log(`- 已备份: ${backupCount} 个文件 (备份目录: ${BACKUP_DIR})`);
  console.log(`- 已跳过: ${skipped} 个文件`);

  // 提示如何恢复备份
  console.log(`\n如需恢复备份，请运行以下命令:`);
  console.log(`cp -r ${BACKUP_DIR}/* ${CONTENT_DIR}/`);
}

// 主函数
function main() {
  // 检查是否有 --help 参数
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
格式化 MDX 文件脚本 (更新版)
使用方法: node src/scripts/format-mdx-files-updated.js [选项]

选项:
  --help, -h                 显示帮助信息
  --dry-run, -d              仅显示将要处理的文件，不进行实际修改
  --reference, -r <文件名>   指定参考文件（默认: ${DEFAULT_REFERENCE_FILE}）

示例:
  node src/scripts/format-mdx-files-updated.js                    # 格式化所有 MDX 文件
  node src/scripts/format-mdx-files-updated.js -d                 # 仅显示将要处理的文件，不进行实际修改
  node src/scripts/format-mdx-files-updated.js -r other.mdx       # 使用不同的参考文件
    `);
    return;
  }

  // 检查是否指定了自定义参考文件
  const referenceArgIndex = process.argv.findIndex(arg => arg === '--reference' || arg === '-r');
  if (referenceArgIndex !== -1 && process.argv.length > referenceArgIndex + 1) {
    const referenceName = process.argv[referenceArgIndex + 1];
    REFERENCE_FILE = getReferenceFilePath(referenceName);
    console.log(`使用自定义参考文件: ${referenceName}`);

    // 检查参考文件是否存在
    if (!fs.existsSync(REFERENCE_FILE)) {
      console.error(`错误: 参考文件不存在 - ${REFERENCE_FILE}`);
      return;
    }
  }

  // 检查是否有 --dry-run 参数
  const isDryRun = process.argv.includes('--dry-run') || process.argv.includes('-d');
  if (isDryRun) {
    console.log('仅预览模式，不会进行实际修改');
    console.log(`使用参考文件: ${path.basename(REFERENCE_FILE)}`);
    const files = getAllMdxFiles();
    console.log(`找到 ${files.length} 个 MDX 文件:`);
    files.forEach(file => {
      if (path.join(CONTENT_DIR, file) !== REFERENCE_FILE) {
        console.log(`- ${file}`);
      } else {
        console.log(`- ${file} (参考文件，将被跳过)`);
      }
    });
    return;
  }

  // 执行处理所有文件
  processAllFiles();
}

// 执行主函数
main();
