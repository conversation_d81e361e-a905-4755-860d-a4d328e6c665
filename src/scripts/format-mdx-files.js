/**
 * 格式化 MDX 文件脚本
 *
 * 此脚本将所有 MDX 文件格式化为与参考文件相同的格式
 * 参考文件: src/content/coloring-pages/puppy-and-bears-sunny-mowing-day-coloring-page.mdx
 *
 * 使用方法:
 * 1. 确保已安装依赖: npm install gray-matter
 * 2. 运行脚本: node src/scripts/format-mdx-files.js
 * 3. 脚本会在格式化前创建备份文件
 */

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

// 默认配置
const CONTENT_DIR = path.join(process.cwd(), 'src/content/coloring-pages');
const DEFAULT_REFERENCE_FILE = 'puppy-and-bears-sunny-mowing-day-coloring-page.mdx';
const BACKUP_DIR = path.join(process.cwd(), 'src/content/coloring-pages-backup');

// 获取参考文件路径
function getReferenceFilePath(customReference = null) {
  if (customReference) {
    // 如果提供了自定义参考文件
    if (customReference.includes('/')) {
      // 如果是相对路径
      return path.resolve(process.cwd(), customReference);
    } else {
      // 如果只是文件名
      return path.join(CONTENT_DIR, customReference);
    }
  }
  // 使用默认参考文件
  return path.join(CONTENT_DIR, DEFAULT_REFERENCE_FILE);
}

// 全局变量，可以通过命令行参数修改
let REFERENCE_FILE = getReferenceFilePath();

// 读取参考文件内容
function readReferenceFile() {
  try {
    const content = fs.readFileSync(REFERENCE_FILE, 'utf8');
    return content;
  } catch (error) {
    console.error(`读取参考文件时出错: ${error.message}`);
    process.exit(1);
  }
}

// 创建备份目录
function createBackupDir() {
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
    console.log(`创建备份目录: ${BACKUP_DIR}`);
  }
}

// 备份文件
function backupFile(filePath) {
  const fileName = path.basename(filePath);
  const backupPath = path.join(BACKUP_DIR, fileName);
  fs.copyFileSync(filePath, backupPath);
  return backupPath;
}

// 获取所有 MDX 文件
function getAllMdxFiles() {
  const files = fs.readdirSync(CONTENT_DIR);
  return files.filter(file => file.endsWith('.mdx') || file.endsWith('.md'));
}

// 格式化 MDX 内容
function formatMdxContent(content) {
  // 使用 gray-matter 解析 frontmatter 和内容
  const parsed = matter(content);

  // 保留原始 frontmatter 数据
  const frontmatter = parsed.data;

  // 确保 frontmatter 中有 categoryInfo 字段（必需字段）
  if (!frontmatter.categoryInfo) {
    // 根据标签或内容推断合适的类别
    const tags = frontmatter.tags || [];
    let mainCategory = 'General';

    if (tags.includes('animals') || tags.includes('dog') || tags.includes('cat') || tags.includes('pet')) {
      mainCategory = 'Animals';
    } else if (tags.includes('mother\'s day') || tags.includes('mom')) {
      mainCategory = 'Family';
    } else if (tags.includes('birthday') || tags.includes('celebration')) {
      mainCategory = 'Celebrations';
    } else if (tags.includes('garden') || tags.includes('gardening') || tags.includes('outdoor')) {
      mainCategory = 'Outdoor Activities';
    }

    // 创建 categoryInfo 对象
    frontmatter.categoryInfo = { main: mainCategory };

    // 尝试确定子分类
    if (mainCategory === 'Animals') {
      if (tags.includes('dog') || tags.includes('puppy')) {
        frontmatter.categoryInfo.sub = 'Dogs';
      } else if (tags.includes('cat') || tags.includes('kitten')) {
        frontmatter.categoryInfo.sub = 'Cats';
      }
    } else if (mainCategory === 'Family') {
      if (tags.includes('mother\'s day')) {
        frontmatter.categoryInfo.sub = 'Mother\'s Day';
      }
    }

    console.log(`添加缺少的 categoryInfo 字段: "${mainCategory}${frontmatter.categoryInfo.sub ? ' > ' + frontmatter.categoryInfo.sub : ''}"`);
  }

  // 移除旧的 category 字段（如果存在）
  if (frontmatter.category) {
    delete frontmatter.category;
  }

  // 获取内容部分
  let mdxContent = parsed.content;

  // 清理内容 - 移除多余的空行
  mdxContent = mdxContent.replace(/\n{3,}/g, '\n\n');

  // 移除水平线
  mdxContent = mdxContent.replace(/^---+$/gm, '');

  // 格式化下载部分 - 移除表格和冗长的描述
  mdxContent = mdxContent.replace(/## ⬇️ Download Your Free Coloring Page[\s\S]*?Choose the format that works best for you:[\s\S]*?(##|$)/g,
    '## ⬇️ Download Your Free Coloring Page\n\n$1');

  // 移除下载格式描述
  mdxContent = mdxContent.replace(/➡️ Download PDF Format \(Ideal for high-quality printing\)/g, '');
  mdxContent = mdxContent.replace(/➡️ Download PNG Format \(Good for digital coloring or resizing\)/g, '');

  // 移除冗长的结尾文本
  mdxContent = mdxContent.replace(/We hope you have a delicious time[\s\S]*?Happy Creating!.*$/g, '');
  mdxContent = mdxContent.replace(/We hope you enjoy coloring[\s\S]*?Happy coloring!.*$/g, '');

  // 将 HTML 标签转换为 Markdown 格式（简单情况）
  mdxContent = mdxContent.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
  mdxContent = mdxContent.replace(/<em>(.*?)<\/em>/g, '*$1*');

  // 格式化标题样式 - 确保使用 ## 和 emoji
  mdxContent = mdxContent.replace(/^# (.*?)$/gm, '## $1');

  // 确保列表项使用 - 而不是 *
  mdxContent = mdxContent.replace(/^\* /gm, '- ');

  // 格式化数字列表 - 确保使用 1. 格式
  mdxContent = mdxContent.replace(/^\d+\.\s+/gm, '1. ');

  // 标准化标题格式
  // 将长标题替换为标准格式
  mdxContent = mdxContent.replace(/^## .*Mom and Daughter.*Hiking.*$/m, '## 🌲 Fun Outdoor Adventure');
  mdxContent = mdxContent.replace(/^## .*Mom and Daughter.*Skiing.*$/m, '## ⛷️ Winter Sports Fun');
  mdxContent = mdxContent.replace(/^## .*Mom.*Reading.*Bedtime.*$/m, '## 📚 Cozy Bedtime Story');
  mdxContent = mdxContent.replace(/^## .*Grandma.*Tea.*$/m, '## ☕ Sweet Tea Time');
  mdxContent = mdxContent.replace(/^## .*Favorite Meal.*$/m, '## 🍽️ Delicious Food Fun');
  mdxContent = mdxContent.replace(/^## .*Mom.*Special Activity.*$/m, '## 💖 Special Activity Page');

  // 确保有标准的部分标题
  if (!mdxContent.includes('## 💖 Why Kids Will Love This Page') &&
      !mdxContent.includes('## 💖 Why You\'ll Love This Page')) {
    // 查找可能的插入点
    const insertPoint = mdxContent.indexOf('## 🎨');
    if (insertPoint !== -1) {
      const whyLoveSection = `\n\n## 💖 Why Kids Will Love This Page\n
- Encourages creativity and imagination
- Develops fine motor skills and color recognition
- Perfect for a quiet afternoon activity
- Creates a sense of accomplishment when completed\n\n`;

      mdxContent = mdxContent.substring(0, insertPoint) + whyLoveSection + mdxContent.substring(insertPoint);
    }
  }

  // 确保有创意着色建议部分
  if (!mdxContent.includes('## 🎨 Creative Coloring Ideas')) {
    // 在文件末尾添加
    mdxContent += `\n\n## 🎨 Creative Coloring Ideas

### Color Scheme Suggestions:
- Try bright, vibrant colors for a cheerful look
- Use soft pastels for a gentle, calming effect
- Experiment with rainbow patterns for extra fun

### Fun Techniques:
- Add patterns or textures to larger areas
- Try shading with colored pencils for depth
- Use metallic or glitter pens for special details\n\n`;
  }

  // 保留特定的 div 样式（如渐变背景）
  // 确保开头和结尾有一个漂亮的渐变背景 div
  const hasOpeningDiv = mdxContent.includes('<div className="text-center my-8 p-4 bg-gradient-to-r');
  const hasClosingDiv = mdxContent.includes('<div className="text-center my-8 p-4 bg-gradient-to-r', mdxContent.lastIndexOf('<div'));

  // 如果没有开头的渐变背景 div，添加一个
  if (!hasOpeningDiv) {
    const gradientDiv = `<div className="text-center my-8 p-4 bg-gradient-to-r from-[#D4FFBD] to-[#A2FFD0] rounded-lg">
  <h3 className="text-[#3EBD93] mt-0">Coloring Fun Awaits!</h3>
  Grab your crayons and get ready for a creative adventure with this delightful coloring page!
</div>\n\n`;
    mdxContent = gradientDiv + mdxContent;
  }

  // 如果没有结尾的渐变背景 div，添加一个
  if (!hasClosingDiv) {
    const closingDiv = `\n\n<div className="text-center my-8 p-4 bg-gradient-to-r from-[#D4FFBD] to-[#A2FFD0] rounded-lg">
  <h3 className="text-[#FF6B6B]">Share Your Masterpiece!</h3>
  <p>We'd love to see how you brought this scene to life! Tag us on social media with <strong>#PrintableColoringHub</strong></p>
</div>`;
    mdxContent = mdxContent + closingDiv;
  }

  // 确保内容以空行开始
  if (!mdxContent.startsWith('\n')) {
    mdxContent = '\n' + mdxContent;
  }

  // 重新组合 frontmatter 和内容
  return matter.stringify(mdxContent, frontmatter);
}

// 处理所有文件
function processAllFiles() {
  // 创建备份目录
  createBackupDir();

  // 读取参考文件
  const referenceContent = readReferenceFile();
  const reference = matter(referenceContent);
  console.log(`成功读取参考文件: ${path.basename(REFERENCE_FILE)}`);
  console.log(`参考文件标题: "${reference.data.title}"`);

  // 获取所有 MDX 文件
  const files = getAllMdxFiles();
  console.log(`找到 ${files.length} 个 MDX 文件`);

  let processed = 0;
  let skipped = 0;
  let backupCount = 0;

  // 显示参考文件信息
  console.log(`使用参考文件: ${path.basename(REFERENCE_FILE)}`);

  // 处理每个文件
  files.forEach(file => {
    const filePath = path.join(CONTENT_DIR, file);

    // 跳过参考文件
    if (filePath === REFERENCE_FILE) {
      console.log(`跳过参考文件: ${file}`);
      skipped++;
      return;
    }

    try {
      // 备份文件
      const backupPath = backupFile(filePath);
      backupCount++;

      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');

      // 格式化内容
      const formattedContent = formatMdxContent(content);

      // 写入文件
      fs.writeFileSync(filePath, formattedContent);

      console.log(`已处理: ${file} (备份至: ${path.basename(backupPath)})`);
      processed++;
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error);
    }
  });

  console.log(`\n处理完成!`);
  console.log(`- 已处理: ${processed} 个文件`);
  console.log(`- 已备份: ${backupCount} 个文件 (备份目录: ${BACKUP_DIR})`);
  console.log(`- 已跳过: ${skipped} 个文件`);

  // 提示如何恢复备份
  console.log(`\n如需恢复备份，请运行以下命令:`);
  console.log(`cp -r ${BACKUP_DIR}/* ${CONTENT_DIR}/`);
}

// 处理单个文件
function processSingleFile(fileName) {
  // 创建备份目录
  createBackupDir();

  // 读取参考文件
  const referenceContent = readReferenceFile();
  const reference = matter(referenceContent);
  console.log(`成功读取参考文件: ${path.basename(REFERENCE_FILE)}`);
  console.log(`参考文件标题: "${reference.data.title}"`);

  // 构建完整文件路径
  let filePath;
  if (fileName.includes('/')) {
    // 如果提供了相对路径
    filePath = path.resolve(process.cwd(), fileName);
  } else {
    // 如果只提供了文件名
    filePath = path.join(CONTENT_DIR, fileName);
  }

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    console.error(`错误: 文件不存在 - ${filePath}`);
    return;
  }

  // 检查是否是 MDX 文件
  if (!filePath.endsWith('.mdx') && !filePath.endsWith('.md')) {
    console.error(`错误: 不是 MDX 文件 - ${filePath}`);
    return;
  }

  // 跳过参考文件
  if (filePath === REFERENCE_FILE) {
    console.log(`跳过参考文件: ${path.basename(filePath)}`);
    return;
  }

  try {
    // 备份文件
    const backupPath = backupFile(filePath);
    console.log(`已备份: ${path.basename(filePath)} -> ${path.basename(backupPath)}`);

    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    const parsed = matter(content);
    console.log(`处理文件: "${parsed.data.title}"`);

    // 格式化内容
    const formattedContent = formatMdxContent(content);

    // 写入文件
    fs.writeFileSync(filePath, formattedContent);

    console.log(`已处理: ${path.basename(filePath)}`);
    console.log(`备份位置: ${backupPath}`);
  } catch (error) {
    console.error(`处理文件 ${path.basename(filePath)} 时出错:`, error);
  }
}

// 添加命令行参数处理
function main() {
  // 检查是否有 --help 参数
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
格式化 MDX 文件脚本
使用方法: node src/scripts/format-mdx-files.js [选项] [文件名]

选项:
  --help, -h                 显示帮助信息
  --dry-run, -d              仅显示将要处理的文件，不进行实际修改
  --file, -f <文件名>        指定要处理的单个文件
  --reference, -r <文件名>   指定参考文件（默认: ${DEFAULT_REFERENCE_FILE}）

示例:
  node src/scripts/format-mdx-files.js                    # 格式化所有 MDX 文件
  node src/scripts/format-mdx-files.js -d                 # 仅显示将要处理的文件，不进行实际修改
  node src/scripts/format-mdx-files.js -f sample.mdx      # 仅处理指定的文件
  node src/scripts/format-mdx-files.js sample.mdx         # 仅处理指定的文件
  node src/scripts/format-mdx-files.js -r other.mdx       # 使用不同的参考文件
    `);
    return;
  }

  // 检查是否指定了自定义参考文件
  const referenceArgIndex = process.argv.findIndex(arg => arg === '--reference' || arg === '-r');
  if (referenceArgIndex !== -1 && process.argv.length > referenceArgIndex + 1) {
    const referenceName = process.argv[referenceArgIndex + 1];
    REFERENCE_FILE = getReferenceFilePath(referenceName);
    console.log(`使用自定义参考文件: ${referenceName}`);

    // 检查参考文件是否存在
    if (!fs.existsSync(REFERENCE_FILE)) {
      console.error(`错误: 参考文件不存在 - ${REFERENCE_FILE}`);
      return;
    }
  }

  // 检查是否指定了单个文件
  const fileArgIndex = process.argv.findIndex(arg => arg === '--file' || arg === '-f');
  if (fileArgIndex !== -1 && process.argv.length > fileArgIndex + 1) {
    // 通过 --file 或 -f 参数指定的文件
    const fileName = process.argv[fileArgIndex + 1];
    console.log(`处理单个文件: ${fileName}`);
    processSingleFile(fileName);
    return;
  } else if (process.argv.length > 2 && !process.argv[2].startsWith('-') &&
             process.argv[2] !== process.argv[referenceArgIndex + 1]) {
    // 直接指定的文件名（确保不是参考文件参数的值）
    const fileName = process.argv[2];
    console.log(`处理单个文件: ${fileName}`);
    processSingleFile(fileName);
    return;
  }

  // 检查是否有 --dry-run 参数
  const isDryRun = process.argv.includes('--dry-run') || process.argv.includes('-d');
  if (isDryRun) {
    console.log('仅预览模式，不会进行实际修改');
    console.log(`使用参考文件: ${path.basename(REFERENCE_FILE)}`);
    const files = getAllMdxFiles();
    console.log(`找到 ${files.length} 个 MDX 文件:`);
    files.forEach(file => {
      if (path.join(CONTENT_DIR, file) !== REFERENCE_FILE) {
        console.log(`- ${file}`);
      } else {
        console.log(`- ${file} (参考文件，将被跳过)`);
      }
    });
    return;
  }

  // 执行处理所有文件
  processAllFiles();
}

// 执行主函数
main();
