/**
 * MDX 文件验证器
 *
 * 一个简单的工具，用于验证 MDX 文件是否符合项目规范
 *
 * 使用方法:
 * 1. 验证所有文件: node src/scripts/mdx-validator.js
 * 2. 验证单个文件: node src/scripts/mdx-validator.js filename.mdx
 * 3. 自动修复所有文件: node src/scripts/mdx-validator.js --fix
 * 4. 自动修复单个文件: node src/scripts/mdx-validator.js filename.mdx --fix
 */

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

// 配置
const CONTENT_DIR = path.join(process.cwd(), 'src/content/coloring-pages');
const BACKUP_DIR = path.join(process.cwd(), 'src/scripts/backups/coloring-pages');

// 验证规则
const rules = [
  {
    name: '必需的 frontmatter 字段',
    validate: (data) => {
      const requiredFields = ['title', 'id', 'assetFolder', 'categoryInfo', 'description'];
      const missingFields = requiredFields.filter(field => !data[field]);
      return {
        valid: missingFields.length === 0,
        message: missingFields.length > 0 ? `缺少必需字段: ${missingFields.join(', ')}` : null,
        fix: (data) => {
          // 修复缺失字段
          if (!data.title && data.id) {
            data.title = data.id.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
          }

          if (!data.id && data.title) {
            data.id = data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-');
          }

          if (!data.assetFolder && data.id) {
            data.assetFolder = data.id;
          }

          if (!data.categoryInfo) {
            data.categoryInfo = { main: 'Uncategorized' };
          }

          if (!data.description && data.title) {
            data.description = `FREE printable coloring page featuring ${data.title}. Perfect for kids of all ages!`;
          }

          return data;
        }
      };
    }
  },
  {
    name: 'categoryInfo 结构',
    validate: (data) => {
      if (!data.categoryInfo) return { valid: false, message: '缺少 categoryInfo 字段' };

      // 检查 categoryInfo 是否是对象且包含 main 字段
      const valid = typeof data.categoryInfo === 'object' && data.categoryInfo.main;

      // 检查 sub 和 subsub 字段是否为空字符串或 null
      let hasEmptyFields = false;
      if (valid) {
        // 只有当字段存在且为空字符串或 null 时，才认为是有问题的
        if (Object.prototype.hasOwnProperty.call(data.categoryInfo, 'sub') &&
            (data.categoryInfo.sub === '' || data.categoryInfo.sub === null)) {
          hasEmptyFields = true;
        }
        if (Object.prototype.hasOwnProperty.call(data.categoryInfo, 'subsub') &&
            (data.categoryInfo.subsub === '' || data.categoryInfo.subsub === null)) {
          hasEmptyFields = true;
        }
      }

      return {
        valid: valid && !hasEmptyFields,
        message: !valid ? 'categoryInfo 结构不正确，必须包含 main 字段' :
                 hasEmptyFields ? 'categoryInfo 中的 sub 或 subsub 字段为空' : null,
        fix: (data) => {
          // 如果 categoryInfo 不是对象或没有 main 字段
          if (typeof data.categoryInfo !== 'object') {
            data.categoryInfo = { main: 'Uncategorized' };
          } else if (!data.categoryInfo.main) {
            data.categoryInfo.main = 'Uncategorized';
          }

          // 移除空的 sub 和 subsub 字段
          if (Object.prototype.hasOwnProperty.call(data.categoryInfo, 'sub') &&
              (data.categoryInfo.sub === '' || data.categoryInfo.sub === null)) {
            delete data.categoryInfo.sub;
          }

          if (Object.prototype.hasOwnProperty.call(data.categoryInfo, 'subsub') &&
              (data.categoryInfo.subsub === '' || data.categoryInfo.subsub === null)) {
            delete data.categoryInfo.subsub;
          }

          return data;
        }
      };
    }
  },
  {
    name: '数组字段格式',
    validate: (data) => {
      const arrayFields = ['tags', 'collections'];
      const invalidFields = arrayFields.filter(field => data[field] && !Array.isArray(data[field]));

      return {
        valid: invalidFields.length === 0,
        message: invalidFields.length > 0 ? `以下字段应为数组: ${invalidFields.join(', ')}` : null,
        fix: (data) => {
          // 确保数组字段是数组
          arrayFields.forEach(field => {
            if (data[field] && !Array.isArray(data[field])) {
              data[field] = [data[field]];
            } else if (!data[field]) {
              data[field] = [];
            }
          });

          return data;
        }
      };
    }
  },
  {
    name: 'tags 内容格式',
    validate: (data) => {
      if (!data.tags || !Array.isArray(data.tags)) {
        return { valid: true, message: null }; // 如果没有 tags 字段或不是数组，由其他规则处理
      }

      // 检查是否有纯数字的标签
      const numericTags = data.tags.filter(tag => /^\d+$/.test(tag));

      return {
        valid: numericTags.length === 0,
        message: numericTags.length > 0 ? `tags 中不能包含纯数字: ${numericTags.join(', ')}` : null,
        fix: (data) => {
          // 移除纯数字标签或转换为字符串描述
          if (data.tags && Array.isArray(data.tags)) {
            data.tags = data.tags.filter(tag => !/^\d+$/.test(tag));

            // 如果过滤后 tags 为空，添加一个默认标签
            if (data.tags.length === 0 && data.title) {
              const defaultTag = data.title.toLowerCase().replace(/[^a-z0-9\s]/g, '').split(' ')[0];
              data.tags = [defaultTag];
            }
          }

          return data;
        }
      };
    }
  },
  {
    name: '布尔字段',
    validate: (data) => {
      const booleanFields = ['featured', 'premium', 'popular'];
      const missingFields = booleanFields.filter(field => data[field] === undefined);

      return {
        valid: missingFields.length === 0,
        message: missingFields.length > 0 ? `缺少布尔字段: ${missingFields.join(', ')}` : null,
        fix: (data) => {
          // 设置默认布尔值
          booleanFields.forEach(field => {
            if (data[field] === undefined) {
              data[field] = false;
            }
          });

          return data;
        }
      };
    }
  },
  {
    name: '日期字段',
    validate: (data) => {
      const valid = data.dateAdded !== undefined;
      return {
        valid,
        message: !valid ? '缺少 dateAdded 字段' : null,
        fix: (data) => {
          if (data.dateAdded === undefined) {
            data.dateAdded = new Date().toISOString();
          }

          return data;
        }
      };
    }
  },
  {
    name: 'dateAdded 格式',
    validate: (data) => {
      // 检查 dateAdded 是否为字符串类型且带有引号
      const isString = typeof data.dateAdded === 'string';
      // 如果是字符串，检查是否是被引号包裹的日期字符串（在YAML中表现为带引号的日期）
      const hasQuotes = isString && /^\s*['"].*['"]\s*$/.test(JSON.stringify(data.dateAdded));

      return {
        valid: !hasQuotes,
        message: hasQuotes ? 'dateAdded 字段不应该带引号' : null,
        fix: (data) => {
          if (typeof data.dateAdded === 'string') {
            // 确保 dateAdded 是一个日期对象，这样在写入时不会带引号
            try {
              // 尝试将字符串转换为日期对象
              const dateStr = data.dateAdded.replace(/^['"]|['"]$/g, '');
              const date = new Date(dateStr);
              if (!isNaN(date.getTime())) {
                // 如果是有效日期，使用ISO字符串格式
                data.dateAdded = date.toISOString();
              }
            } catch (e) {
              // 如果转换失败，使用当前日期
              data.dateAdded = new Date().toISOString();
            }
          }
          return data;
        }
      };
    }
  }
];

// 内容验证规则 - 只检查数字列表格式
const contentRules = [
  {
    name: '数字列表格式',
    validate: (content) => {
      const incorrectNumbering = /^\d+\.\s+/gm.test(content) && !/^1\.\s+/gm.test(content);
      return {
        valid: !incorrectNumbering,
        message: incorrectNumbering ? '数字列表格式不正确，应使用 1. 格式' : null,
        fix: (content) => {
          return content.replace(/^\d+\.\s+/gm, '1. ');
        }
      };
    }
  }
];

// 创建备份目录
function createBackupDir() {
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
    console.log(`创建备份目录: ${BACKUP_DIR}`);
  }
}

// 备份文件
function backupFile(filePath) {
  const fileName = path.basename(filePath);
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFileName = `${fileName.replace('.mdx', '')}-${timestamp}.mdx`;
  const backupPath = path.join(BACKUP_DIR, backupFileName);

  fs.copyFileSync(filePath, backupPath);
  return backupPath;
}

// 获取所有 MDX 文件
function getAllMdxFiles() {
  const files = fs.readdirSync(CONTENT_DIR);
  return files.filter(file => file.endsWith('.mdx') || file.endsWith('.md'));
}

// 验证单个文件
function validateFile(filePath, autoFix = false) {
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    const { data, content: mdxContent } = matter(content);

    console.log(`\n验证文件: ${path.basename(filePath)}`);

    // 验证 frontmatter
    let isValid = true;
    let needsFix = false;
    let fixedData = { ...data };

    // 应用 frontmatter 验证规则
    for (const rule of rules) {
      const result = rule.validate(data);
      if (!result.valid) {
        console.log(`❌ ${rule.name}: ${result.message}`);
        isValid = false;
        needsFix = true;

        if (autoFix && result.fix) {
          fixedData = result.fix(fixedData);
          console.log(`  ✓ 已修复: ${rule.name}`);
        }
      } else {
        console.log(`✓ ${rule.name}`);
      }
    }

    // 验证内容
    let fixedContent = mdxContent;

    // 应用内容验证规则
    for (const rule of contentRules) {
      const result = rule.validate(mdxContent);
      if (!result.valid) {
        console.log(`❌ ${rule.name}: ${result.message}`);
        isValid = false;
        needsFix = true;

        if (autoFix && typeof result.fix === 'function') {
          fixedContent = result.fix(fixedContent);
          console.log(`  ✓ 已修复: ${rule.name}`);
        }
      } else {
        console.log(`✓ ${rule.name}`);
      }
    }

    // 如果需要修复并且启用了自动修复
    if (needsFix && autoFix) {
      // 备份文件
      const backupPath = backupFile(filePath);
      console.log(`已备份: ${path.basename(filePath)} -> ${path.basename(backupPath)}`);

      // 重新组合 frontmatter 和内容
      let fixedFileContent = matter.stringify(fixedContent, fixedData);

      // 直接替换文件内容中的带引号的dateAdded字段
      fixedFileContent = fixedFileContent.replace(/(dateAdded:\s*)['"]([^'"]+)['"]/g, '$1$2');

      // 写入文件
      fs.writeFileSync(filePath, fixedFileContent);
      console.log(`已修复并保存: ${path.basename(filePath)}`);
    }

    return { isValid, needsFix };
  } catch (error) {
    console.error(`处理文件 ${path.basename(filePath)} 时出错:`, error);
    return { isValid: false, needsFix: true, error };
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const autoFix = args.includes('--fix');

  // 如果指定了自动修复，创建备份目录
  if (autoFix) {
    createBackupDir();
  }

  // 检查是否指定了文件名
  const fileArg = args.find(arg => !arg.startsWith('--'));

  if (fileArg) {
    // 处理单个文件
    let filePath;
    if (fileArg.includes('/')) {
      // 如果提供了相对路径
      filePath = path.resolve(process.cwd(), fileArg);
    } else {
      // 如果只提供了文件名
      filePath = path.join(CONTENT_DIR, fileArg);
    }

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.error(`错误: 文件不存在 - ${filePath}`);
      return;
    }

    // 检查是否是 MDX 文件
    if (!filePath.endsWith('.mdx') && !filePath.endsWith('.md')) {
      console.error(`错误: 不是 MDX 文件 - ${filePath}`);
      return;
    }

    validateFile(filePath, autoFix);
  } else {
    // 处理所有文件
    const files = getAllMdxFiles();
    console.log(`找到 ${files.length} 个 MDX 文件`);

    let validCount = 0;
    let invalidCount = 0;
    let fixedCount = 0;

    // 处理每个文件
    for (const file of files) {
      const filePath = path.join(CONTENT_DIR, file);
      const { isValid, needsFix } = validateFile(filePath, autoFix);

      if (isValid) {
        validCount++;
      } else {
        invalidCount++;
        if (autoFix && needsFix) {
          fixedCount++;
        }
      }
    }

    // 显示统计信息
    console.log(`\n验证完成!`);
    console.log(`- 总文件数: ${files.length}`);
    console.log(`- 有效文件: ${validCount}`);
    console.log(`- 无效文件: ${invalidCount}`);

    if (autoFix) {
      console.log(`- 已修复文件: ${fixedCount}`);
    } else if (invalidCount > 0) {
      console.log(`\n提示: 使用 --fix 参数可以自动修复问题`);
    }
  }
}

// 执行主函数
main();
