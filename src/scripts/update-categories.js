/**
 * 更新着色页面分类脚本
 *
 * 此脚本将自动更新所有着色页面的分类信息，添加二级分类结构
 * 使用方法: node src/scripts/update-categories.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import matter from 'gray-matter';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 内容目录
const CONTENT_DIR = path.join(process.cwd(), 'src/content/coloring-pages');

// 分类映射
const CATEGORY_MAPPING = {
  // 动物分类
  'Animals': {
    keywords: ['dog', 'puppy', 'cat', 'kitten', 'pet', 'animal'],
    subcategories: {
      'Dogs': {
        keywords: ['dog', 'puppy', 'corgi', 'retriever', 'mastiff', 'husky', 'shepherd', 'dachshund', 'basset', 'shiba', 'canine']
      },
      'Cats': {
        keywords: ['cat', 'kitten', 'feline', 'tabby', 'kitty', 'caticorn']
      },
      'Butterflies': {
        keywords: ['butterfly', 'butterflies', 'wing']
      }
    }
  },

  // 家庭分类
  'Family': {
    keywords: ['family', 'mom', 'mother', 'daughter', 'son', 'kid', 'parent', 'grandma', 'granddaughter'],
    subcategories: {
      'Mother\'s Day': {
        keywords: ['mother\'s day', 'mom', 'mother']
      },
      'Grandparent Time': {
        keywords: ['grandma', 'grandmother', 'granddaughter', 'grandson']
      },
      'Family Activities': {
        keywords: ['family', 'celebration', 'activity', 'together']
      }
    }
  },

  // 节日分类
  'Holidays': {
    keywords: ['holiday', 'christmas', 'halloween', 'birthday', 'festive', 'celebration'],
    subcategories: {
      'Christmas': {
        keywords: ['christmas', 'santa', 'holiday season', 'festive']
      },
      'Halloween': {
        keywords: ['halloween', 'spooky', 'witch']
      },
      'Birthday': {
        keywords: ['birthday', 'party', 'cake', 'gift', 'celebration']
      }
    }
  },

  // 室内场景
  'Indoor Scenes': {
    keywords: ['bedroom', 'kitchen', 'workshop', 'indoor', 'room', 'home', 'house'],
    subcategories: {
      'Bedroom': {
        keywords: ['bedroom', 'bed', 'sleeping', 'bedtime']
      },
      'Kitchen': {
        keywords: ['kitchen', 'cooking', 'baking', 'food', 'meal']
      },
      'Workshop': {
        keywords: ['workshop', 'craft', 'studio', 'work']
      }
    }
  },

  // 户外活动
  'Outdoor Activities': {
    keywords: ['outdoor', 'garden', 'travel', 'hiking', 'skiing', 'beach', 'nature', 'adventure'],
    subcategories: {
      'Garden': {
        keywords: ['garden', 'gardening', 'flower', 'plant', 'meadow']
      },
      'Travel': {
        keywords: ['travel', 'traveling', 'adventure', 'journey', 'trip']
      },
      'Sports': {
        keywords: ['sport', 'skiing', 'hiking', 'ballet', 'dance', 'activity']
      }
    }
  }
};

// 获取所有MDX文件
function getAllMdxFiles() {
  const files = fs.readdirSync(CONTENT_DIR);
  return files.filter(file => file.endsWith('.mdx') || file.endsWith('.md'));
}

// 确定最佳分类
function determineBestCategory(frontmatter) {
  const title = frontmatter.title || '';
  const description = frontmatter.description || '';
  const tags = frontmatter.tags || [];
  const currentCategory = frontmatter.category || '';

  // 合并所有文本以进行关键词匹配
  const allText = [
    title,
    description,
    ...tags,
    currentCategory
  ].join(' ').toLowerCase();

  // 评分系统
  const categoryScores = {};

  // 为每个主分类和子分类计算分数
  for (const [mainCategory, mainCategoryData] of Object.entries(CATEGORY_MAPPING)) {
    // 初始化主分类分数
    categoryScores[mainCategory] = {
      score: 0,
      subcategories: {}
    };

    // 计算主分类关键词匹配分数
    for (const keyword of mainCategoryData.keywords) {
      if (allText.includes(keyword.toLowerCase())) {
        categoryScores[mainCategory].score += 1;
      }
    }

    // 如果当前分类与主分类匹配，增加分数
    if (currentCategory.toLowerCase() === mainCategory.toLowerCase()) {
      categoryScores[mainCategory].score += 5;
    }

    // 计算子分类分数
    for (const [subCategory, subCategoryData] of Object.entries(mainCategoryData.subcategories)) {
      categoryScores[mainCategory].subcategories[subCategory] = 0;

      // 计算子分类关键词匹配分数
      for (const keyword of subCategoryData.keywords) {
        if (allText.includes(keyword.toLowerCase())) {
          categoryScores[mainCategory].subcategories[subCategory] += 1;
          // 同时增加主分类的分数
          categoryScores[mainCategory].score += 0.5;
        }
      }
    }
  }

  // 找出得分最高的主分类
  let bestMainCategory = '';
  let bestMainScore = -1;

  for (const [mainCategory, data] of Object.entries(categoryScores)) {
    if (data.score > bestMainScore) {
      bestMainScore = data.score;
      bestMainCategory = mainCategory;
    }
  }

  // 如果没有找到合适的主分类，使用默认分类
  if (bestMainScore <= 0) {
    return { main: 'Other', sub: null };
  }

  // 找出得分最高的子分类
  let bestSubCategory = '';
  let bestSubScore = -1;

  for (const [subCategory, score] of Object.entries(categoryScores[bestMainCategory].subcategories)) {
    if (score > bestSubScore) {
      bestSubScore = score;
      bestSubCategory = subCategory;
    }
  }

  // 如果子分类得分太低，不使用子分类
  if (bestSubScore <= 0) {
    return { main: bestMainCategory, sub: null };
  }

  return { main: bestMainCategory, sub: bestSubCategory };
}

// 更新MDX文件的分类
function updateCategoryInFile(filePath, newCategory) {
  try {
    // 读取文件内容
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // 解析frontmatter
    const { data, content } = matter(fileContent);

    // 更新分类信息
    data.categoryInfo = {
      main: newCategory.main
    };

    // 如果有子分类，添加到categoryInfo中
    if (newCategory.sub) {
      data.categoryInfo.sub = newCategory.sub;
    }

    // 重新组合文件内容
    const updatedFileContent = matter.stringify(content, data);

    // 写入文件
    fs.writeFileSync(filePath, updatedFileContent);

    return true;
  } catch (error) {
    console.error(`更新文件 ${filePath} 时出错:`, error);
    return false;
  }
}

// 主函数
function main() {
  console.log('开始更新着色页面分类...');

  // 获取所有MDX文件
  const files = getAllMdxFiles();
  console.log(`找到 ${files.length} 个MDX文件`);

  let updatedCount = 0;
  const categoryStats = {};

  // 处理每个文件
  for (const file of files) {
    const filePath = path.join(CONTENT_DIR, file);

    try {
      // 读取文件内容
      const fileContent = fs.readFileSync(filePath, 'utf8');

      // 解析frontmatter
      const { data } = matter(fileContent);

      // 确定最佳分类
      const bestCategory = determineBestCategory(data);

      // 更新文件
      const updated = updateCategoryInFile(filePath, bestCategory);

      if (updated) {
        updatedCount++;

        // 更新统计信息
        const categoryKey = bestCategory.sub
          ? `${bestCategory.main} > ${bestCategory.sub}`
          : bestCategory.main;

        if (!categoryStats[categoryKey]) {
          categoryStats[categoryKey] = 0;
        }
        categoryStats[categoryKey]++;

        console.log(`已更新: ${file} => ${categoryKey}`);
      }
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error);
    }
  }

  console.log(`\n更新完成! 已更新 ${updatedCount} 个文件`);
  console.log('\n分类统计:');

  // 显示分类统计
  for (const [category, count] of Object.entries(categoryStats)) {
    console.log(`- ${category}: ${count} 个文件`);
  }
}

// 执行主函数
main();
