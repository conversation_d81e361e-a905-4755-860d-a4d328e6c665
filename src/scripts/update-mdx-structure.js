/**
 * 更新MDX文件结构脚本
 * 
 * 这个脚本会遍历所有MDX文件，将旧的category字段转换为新的categoryInfo结构
 */

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 内容目录路径
const contentDir = path.join(__dirname, '..', 'content', 'coloring-pages');

// 解析category字符串为categoryInfo对象
function parseCategoryString(category) {
  if (!category) return { main: 'Uncategorized' };
  
  // 检查category是否包含分隔符（例如：'Animals > Dogs'）
  if (category.includes('>')) {
    const parts = category.split('>').map(part => part.trim());
    
    // 根据部分数量创建不同的对象
    if (parts.length === 2) {
      return { main: parts[0], sub: parts[1] };
    } else if (parts.length >= 3) {
      return { main: parts[0], sub: parts[1], subsub: parts[2] };
    }
  }
  
  // 如果没有分隔符，则整个category作为main
  return { main: category };
}

// 更新MDX文件
function updateMdxFile(filePath) {
  try {
    // 读取文件内容
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // 解析frontmatter
    const { data, content } = matter(fileContent);
    
    // 如果已经有categoryInfo，跳过
    if (data.categoryInfo) {
      console.log(`跳过 ${path.basename(filePath)} - 已有categoryInfo`);
      return;
    }
    
    // 如果没有category字段，添加默认分类
    if (!data.category) {
      data.categoryInfo = { main: 'Uncategorized' };
      console.log(`更新 ${path.basename(filePath)} - 添加默认分类`);
    } else {
      // 解析category字符串
      data.categoryInfo = parseCategoryString(data.category);
      console.log(`更新 ${path.basename(filePath)} - 从 "${data.category}" 转换为结构化对象`);
      
      // 删除旧的category字段
      delete data.category;
    }
    
    // 添加collections字段（如果没有）
    if (!data.collections) {
      data.collections = [];
    }
    
    // 添加featured字段（如果没有）
    if (data.featured === undefined) {
      data.featured = false;
    }
    
    // 添加premium字段（如果没有）
    if (data.premium === undefined) {
      data.premium = false;
    }
    
    // 重新组合文件内容
    const updatedFileContent = matter.stringify(content, data);
    
    // 写入文件
    fs.writeFileSync(filePath, updatedFileContent);
    
    return true;
  } catch (error) {
    console.error(`更新文件 ${filePath} 时出错:`, error);
    return false;
  }
}

// 遍历目录中的所有MDX文件
function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  let updatedCount = 0;
  let errorCount = 0;
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 如果是目录，递归处理
      const result = processDirectory(filePath);
      updatedCount += result.updatedCount;
      errorCount += result.errorCount;
    } else if (file.endsWith('.mdx') || file.endsWith('.md')) {
      // 如果是MDX文件，更新它
      const success = updateMdxFile(filePath);
      if (success) {
        updatedCount++;
      } else {
        errorCount++;
      }
    }
  }
  
  return { updatedCount, errorCount };
}

// 主函数
function main() {
  console.log('开始更新MDX文件结构...');
  
  const result = processDirectory(contentDir);
  
  console.log(`更新完成！成功更新 ${result.updatedCount} 个文件，失败 ${result.errorCount} 个文件。`);
}

// 执行主函数
main();
