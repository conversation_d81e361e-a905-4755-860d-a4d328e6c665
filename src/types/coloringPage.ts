import type { ImageMetadata } from 'astro:assets';

// 定义分类结构
export interface Category {
  main: string;       // 主分类
  sub?: string;       // 子分类（可选）
  subsub?: string;    // 孙分类（可选）
}

// 定义着色页面的类型
export interface ColoringPage {
  id: string;
  slug: string;
  title: string;

  // 分类系统（结构化）
  categoryInfo: Category; // 结构化分类信息

  // 集合系统
  collections?: string[]; // 集合名称数组，例如：['Holidays', 'Christmas']

  // 资源文件夹
  assetFolder: string; // 资源文件夹名称

  // 资源文件会通过assetFolder自动关联，不需要单独指定文件名

  // 资源文件导入对象（由 [id].astro 页面动态导入）
  monochromePngImage?: ImageMetadata;  // 导入的黑白 PNG 图像对象
  coloredPngImage?: ImageMetadata;     // 导入的彩色参考图对象

  // 其他字段
  dateAdded?: string | Date;
  popular?: boolean;
  description?: string;
  tags?: string[];
  featured?: boolean; // 是否推荐
  premium?: boolean; // 是否付费内容
}
