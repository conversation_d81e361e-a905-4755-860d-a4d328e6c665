// Cloudflare R2和图像优化工具函数
import {
  buildR2Url as configBuildR2Url,
  CLOUDFLARE_IMAGES
} from '@/config/cloudflare';

// 缓存已加载的图片URL，避免重复请求
const imageCache = new Map<string, string>();

// 文件类型定义
type FileType = 'monochrome.png' | 'monochrome.pdf' | 'colored.png';

// 图片变换选项类型
export interface ImageTransformOptions {
  width?: number;
  height?: number;
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  quality?: number;
  format?: 'auto' | 'webp' | 'avif' | 'jpeg' | 'png';
  dpr?: number;
  gravity?: 'auto' | 'center' | 'north' | 'south' | 'east' | 'west';
}

/**
 * 根据资源文件夹和文件类型生成可能的文件名数组
 * @param assetFolder 资源文件夹名称
 * @param fileType 文件类型
 * @returns 可能的文件名数组，按优先级排序
 */
function getPossibleFilenames(assetFolder: string, fileType: FileType): string[] {
  // 获取资源文件夹的最后一部分（用于构建文件名）
  const folderParts = assetFolder.split('/');
  const lastFolderPart = folderParts[folderParts.length - 1];

  // 根据正确的命名规则，我们应该使用 ${lastFolderPart}_monochrome.png 这样的格式
  switch (fileType) {
    case 'monochrome.png':
      return [
        `${lastFolderPart}_monochrome.png`,   // 正确的命名格式
        'monochrome.png'                      // 备用格式（向后兼容）
      ];
    case 'monochrome.pdf':
      return [
        `${lastFolderPart}_monochrome.pdf`,   // 正确的命名格式
        'monochrome.pdf'                      // 备用格式（向后兼容）
      ];
    case 'colored.png':
      return [
        `${lastFolderPart}_colored.png`,      // 正确的命名格式
        'colored.png'                         // 备用格式（向后兼容）
      ];
    default:
      return [fileType];
  }
}

/**
 * 构建Cloudflare R2资源URL，支持图像大小调整
 * @param assetFolder 资源文件夹名称
 * @param filename 文件名
 * @param options 图片优化选项
 * @returns 完整的R2资源URL
 */
export function buildR2Url(
  assetFolder: string,
  filename: string,
  options: ImageTransformOptions = {}
): string {
  return configBuildR2Url(assetFolder, filename, options);
}

/**
 * 获取图片URL（用于下载链接）
 * @param assetFolder 资源文件夹名称
 * @param filename 文件名（不含路径）
 * @returns 图片URL字符串
 */
export function getR2ImageUrl(assetFolder: string, filename: string): string {
  try {
    if (!assetFolder || !filename) {
      console.error('Asset folder or filename is missing', { assetFolder, filename });
      return '';
    }

    // 构建缓存键
    const cacheKey = `url:${assetFolder}/${filename}`;

    // 检查缓存中是否已存在
    if (imageCache.has(cacheKey)) {
      return imageCache.get(cacheKey) as string;
    }

    // 获取可能的文件名列表
    const possibleFilenames = getPossibleFilenames(assetFolder, filename as FileType);
    if (!possibleFilenames.length) {
      console.error('No possible filenames found', { assetFolder, filename });
      return '';
    }

    // 构建所有可能的URL
    // 注意：我们假设文件已经上传到R2，并且使用了相同的命名规则之一
    // 我们将尝试所有可能的文件名，并返回第一个可用的URL
    // 由于我们无法在服务器端检查文件是否存在，我们返回所有可能的URL，用逗号分隔
    // 客户端将尝试加载第一个URL，如果失败，将尝试下一个
    const urls = possibleFilenames
      .map(name => buildR2Url(assetFolder, name))
      .filter(url => url); // 过滤掉空URL

    if (!urls.length) {
      console.error('No valid URLs generated', { assetFolder, filename });
      return '';
    }

    const r2Url = urls.join(',');

    // 存入缓存
    imageCache.set(cacheKey, r2Url);
    return r2Url;
  } catch (error) {
    console.error(`Error getting R2 URL for ${assetFolder}/${filename}:`, error);

    // 如果出错，返回一个基于所有可能文件名的URL
    try {
      const possibleFilenames = getPossibleFilenames(assetFolder, filename as FileType);
      const urls = possibleFilenames
        .map(name => buildR2Url(assetFolder, name))
        .filter(url => url); // 过滤掉空URL

      return urls.length ? urls.join(',') : '';
    } catch (fallbackError) {
      console.error('Fallback error:', fallbackError);
      return '';
    }
  }
}

/**
 * 获取图片URL，支持图片变换（用于显示）
 * @param assetFolder 资源文件夹名称
 * @param filename 文件名（不含路径）
 * @param options 图片变换选项
 * @returns 图片URL字符串
 */
export function getCloudflareImagesUrl(
  assetFolder: string,
  filename: string,
  options: ImageTransformOptions = {}
): string {
  try {
    if (!assetFolder || !filename) {
      console.error('Asset folder or filename is missing', { assetFolder, filename });
      return '';
    }

    // 构建缓存键（包含选项）
    const optionsKey = JSON.stringify(options);
    const cacheKey = `url:${assetFolder}/${filename}:${optionsKey}`;

    // 检查缓存中是否已存在
    if (imageCache.has(cacheKey)) {
      return imageCache.get(cacheKey) as string;
    }

    // 获取可能的文件名列表
    const possibleFilenames = getPossibleFilenames(assetFolder, filename as FileType);
    if (!possibleFilenames.length) {
      console.error('No possible filenames found', { assetFolder, filename });
      return '';
    }

    // 使用Cloudflare图像大小调整功能
    // 我们将尝试所有可能的文件名，并返回第一个可用的URL
    // 由于我们无法在服务器端检查文件是否存在，我们返回所有可能的URL，用逗号分隔
    // 客户端将尝试加载第一个URL，如果失败，将尝试下一个
    const urls = possibleFilenames
      .map(name => {
        // 为每个可能的文件名构建优化的图像URL
        return buildR2Url(assetFolder, name, options);
      })
      .filter(url => url); // 过滤掉空URL

    if (!urls.length) {
      console.error('No valid URLs generated', { assetFolder, filename });
      return '';
    }

    const imageUrl = urls.join(',');

    // 存入缓存
    imageCache.set(cacheKey, imageUrl);
    return imageUrl;
  } catch (error) {
    console.error(`Error getting optimized image URL for ${assetFolder}/${filename}:`, error);

    // 如果出错，返回一个基本URL（不带优化参数）
    try {
      const possibleFilenames = getPossibleFilenames(assetFolder, filename as FileType);
      const urls = possibleFilenames
        .map(name => buildR2Url(assetFolder, name))
        .filter(url => url); // 过滤掉空URL

      return urls.length ? urls.join(',') : '';
    } catch (fallbackError) {
      console.error('Fallback error:', fallbackError);
      return '';
    }
  }
}
