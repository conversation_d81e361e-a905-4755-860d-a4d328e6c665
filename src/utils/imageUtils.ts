// 图片工具函数
// Cloudflare R2配置
const CLOUDFLARE_R2_PUBLIC_URL = 'https://static.printablecoloringhub.com';
const CLOUDFLARE_R2_BASE_PATH = 'coloring-pages';

// 默认图片URL
export const defaultColoredReferenceUrl = `${CLOUDFLARE_R2_PUBLIC_URL}/default-colored-reference.png`;

// 缓存已获取的图片URL，避免重复请求
const imageCache = new Map<string, string>();

// 文件类型定义
type FileType = 'monochrome.png' | 'monochrome.pdf' | 'colored.png';

// Cloudflare Images配置
export const CLOUDFLARE_IMAGES = {
  // 图片变换预设
  presets: {
    // 缩略图预设
    thumbnail: {
      width: 300,
      height: 300,
      fit: 'cover',
      quality: 80,
      format: 'webp'
    },
    // 卡片预设
    card: {
      width: 500,
      height: 500,
      fit: 'cover',
      quality: 80,
      format: 'webp'
    },
    // 详情页预设
    detail: {
      width: 800,
      height: 800,
      fit: 'contain',
      quality: 85,
      format: 'webp'
    },
    // 全尺寸预设
    full: {
      width: 1200,
      height: 1200,
      fit: 'contain',
      quality: 90,
      format: 'webp'
    }
  }
};

/**
 * 根据资源文件夹和文件类型生成可能的文件名数组
 * @param assetFolder 资源文件夹名称
 * @param fileType 文件类型
 * @returns 可能的文件名数组，按优先级排序
 */
function getPossibleFilenames(assetFolder: string, fileType: FileType): string[] {
  switch (fileType) {
    case 'monochrome.png':
      return [
        `${assetFolder}_monochrome.png`,     // 新命名格式
        `${assetFolder}_m.png`             // 短命名格式
      ];
    case 'monochrome.pdf':
      return [
        `${assetFolder}_monochrome.pdf`,     // 新命名格式
        `${assetFolder}_m.pdf`
      ];
    case 'colored.png':
      return [
        `${assetFolder}_colored.png`,        // 新命名格式
        `${assetFolder}_c.png`
      ];
    default:
      return [fileType];
  }
}

/**
 * 构建Cloudflare R2 URL
 * @param assetFolder 资源文件夹名称
 * @param filename 文件名
 * @returns Cloudflare R2 URL
 */
function buildR2Url(assetFolder: string, filename: string): string {
  // 确保assetFolder和filename都存在
  if (!assetFolder || !filename) {
    console.error('Asset folder or filename is missing', { assetFolder, filename });
    return '';
  }

  // 移除可能的前导斜杠
  const cleanAssetFolder = assetFolder.replace(/^\/+/, '');
  const cleanFilename = filename.replace(/^\/+/, '');

  return `${CLOUDFLARE_R2_PUBLIC_URL}/${CLOUDFLARE_R2_BASE_PATH}/${cleanAssetFolder}/${cleanFilename}`;
}

/**
 * 获取图片URL（用于下载链接）
 * @param assetFolder 资源文件夹名称
 * @param filename 文件名（不含路径）
 * @returns 图片URL字符串
 */
export function getImageUrl(assetFolder: string, filename: string): string {
  try {
    if (!assetFolder || !filename) {
      console.error('Asset folder or filename is missing', { assetFolder, filename });
      return '';
    }

    // 构建缓存键
    const cacheKey = `url:${assetFolder}/${filename}`;

    // 检查缓存中是否已存在
    if (imageCache.has(cacheKey)) {
      return imageCache.get(cacheKey) as string;
    }

    // 获取可能的文件名列表
    const possibleFilenames = getPossibleFilenames(assetFolder, filename as FileType);
    if (!possibleFilenames.length) {
      console.error('No possible filenames found', { assetFolder, filename });
      return '';
    }

    // 构建所有可能的URL
    const urls = possibleFilenames
      .map(name => buildR2Url(assetFolder, name))
      .filter(url => url); // 过滤掉空URL

    if (!urls.length) {
      console.error('No valid URLs generated', { assetFolder, filename });
      return '';
    }

    const r2Url = urls.join(',');

    // 存入缓存
    imageCache.set(cacheKey, r2Url);
    return r2Url;
  } catch (error) {
    console.error(`Error getting R2 URL for ${assetFolder}/${filename}:`, error);

    // 如果出错，返回一个基于所有可能文件名的URL
    try {
      const possibleFilenames = getPossibleFilenames(assetFolder, filename as FileType);
      const urls = possibleFilenames
        .map(name => buildR2Url(assetFolder, name))
        .filter(url => url); // 过滤掉空URL

      return urls.length ? urls.join(',') : '';
    } catch (fallbackError) {
      console.error('Fallback error:', fallbackError);
      return '';
    }
  }
}

/**
 * 获取图片URL（同步版本，用于客户端脚本）
 * @param assetFolder 资源文件夹名称
 * @param filename 文件名（不含路径）
 * @returns 图片URL字符串
 */
export function getImageUrlSync(assetFolder: string, filename: string): string {
  return getImageUrl(assetFolder, filename);
}

/**
 * 获取图片URL，支持图片变换（用于显示）
 * @param assetFolder 资源文件夹名称
 * @param filename 文件名（不含路径）
 * @param options 图片变换选项
 * @returns 图片URL字符串
 */
export function getCloudflareImagesUrl(
  assetFolder: string,
  filename: string,
  options: {
    width?: number;
    height?: number;
    fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
    quality?: number;
    format?: 'webp' | 'avif' | 'json' | 'jpeg' | 'png';
  } = {}
): string {
  try {
    if (!assetFolder || !filename) {
      console.error('Asset folder or filename is missing', { assetFolder, filename });
      return '';
    }

    // 构建缓存键（包含选项）
    const optionsKey = JSON.stringify(options);
    const cacheKey = `url:${assetFolder}/${filename}:${optionsKey}`;

    // 检查缓存中是否已存在
    if (imageCache.has(cacheKey)) {
      return imageCache.get(cacheKey) as string;
    }

    // 获取可能的文件名列表
    const possibleFilenames = getPossibleFilenames(assetFolder, filename as FileType);
    if (!possibleFilenames.length) {
      console.error('No possible filenames found', { assetFolder, filename });
      return '';
    }

    // 构建带有变换选项的URL
    const urls = possibleFilenames
      .map(name => {
        const baseUrl = buildR2Url(assetFolder, name);
        if (!baseUrl) return '';

        // 如果没有变换选项，直接返回基本URL
        if (Object.keys(options).length === 0) {
          return baseUrl;
        }

        // 构建Cloudflare Images变换URL
        const params = new URLSearchParams();

        if (options.width) params.append('width', options.width.toString());
        if (options.height) params.append('height', options.height.toString());
        if (options.fit) params.append('fit', options.fit);
        if (options.quality) params.append('quality', options.quality.toString());
        if (options.format) params.append('format', options.format);

        // 返回带有变换参数的URL
        return `${baseUrl}?${params.toString()}`;
      })
      .filter(url => url); // 过滤掉空URL

    if (!urls.length) {
      console.error('No valid URLs generated', { assetFolder, filename });
      return '';
    }

    const imageUrl = urls.join(',');

    // 存入缓存
    imageCache.set(cacheKey, imageUrl);
    return imageUrl;
  } catch (error) {
    console.error(`Error getting Cloudflare Images URL for ${assetFolder}/${filename}:`, error);

    // 如果出错，返回一个基本URL
    try {
      const possibleFilenames = getPossibleFilenames(assetFolder, filename as FileType);
      const urls = possibleFilenames
        .map(name => buildR2Url(assetFolder, name))
        .filter(url => url); // 过滤掉空URL

      return urls.length ? urls.join(',') : '';
    } catch (fallbackError) {
      console.error('Fallback error:', fallbackError);
      return '';
    }
  }
}

/**
 * 检查图片是否存在
 * 由于我们使用Cloudflare R2，我们无法在服务器端检查文件是否存在
 * 所以这个函数总是返回true
 * @param _assetFolder 资源文件夹名称
 * @param _filename 文件名（不含路径）
 * @returns 是否存在
 */
export function imageExists(_assetFolder: string, _filename: string): boolean {
  return true;
}
