import type { Config } from 'tailwindcss';
import typography from '@tailwindcss/typography';

const config: Config = {
  content: [
    './src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}',
    './node_modules/flowbite/**/*.js'
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        display: ['Poppins', 'sans-serif'],
      },
      // 启用 line-clamp 功能
      lineClamp: {
        1: '1',
        2: '2',
        3: '3',
      },
      colors: {
        primary: {
          DEFAULT: '#f80',
          hover: '#e67700',
          light: 'rgba(255, 136, 0, 0.1)',
        },
        secondary: {
          DEFAULT: '#64748B',
          hover: '#475569',
          light: 'rgba(100, 116, 139, 0.1)',
        },
        accent: {
          DEFAULT: '#10B981',
          light: 'rgba(16, 185, 129, 0.1)',
        },
        text: {
          DEFAULT: '#1F2937',
          light: '#4B5563',
        },
        background: {
          DEFAULT: '#F9FAFB',
          alt: '#F3F4F6',
        },
        card: '#FFFFFF',
        border: '#E5E7EB',
      },
      boxShadow: {
        'sm-custom': '0 1px 2px rgba(0, 0, 0, 0.05)',
        'custom': '0 1px 3px rgba(0, 0, 0, 0.1)',
      },
      typography: {
        DEFAULT: {
          css: {
            color: 'var(--color-text)',
            a: {
              color: 'var(--color-primary)',
              '&:hover': {
                color: 'var(--color-primary-hover)',
              },
            },
            h1: {
              color: 'var(--color-text)',
              fontFamily: 'var(--font-display)',
            },
            h2: {
              color: 'var(--color-text)',
              fontFamily: 'var(--font-display)',
            },
            h3: {
              color: 'var(--color-text)',
              fontFamily: 'var(--font-display)',
            },
          },
        },
      },
    },
  },
  plugins: [
    typography,
    require('flowbite/plugin'),
  ],
};

export default config;
