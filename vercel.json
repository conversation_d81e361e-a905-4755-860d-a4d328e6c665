{"version": 2, "buildCommand": "yarn build", "installCommand": "yarn install", "routes": [{"src": "/_astro/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "continue": true}], "build": {"env": {"SHARP_IGNORE_GLOBAL_LIBVIPS": "true", "SHARP_DIST_BASE_URL": "https://registry.npmjs.org/sharp/-/sharp-0.33.5.tgz", "NODE_OPTIONS": "--max-old-space-size=4096 --max-http-header-size=16384 --no-warnings"}}}